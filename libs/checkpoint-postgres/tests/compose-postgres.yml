services:
  postgres-test:
    image: pgvector/pgvector:pg${POSTGRES_VERSION:-16}
    ports:
      - "5441:5432"
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    command: ["postgres", "-c", "shared_preload_libraries=vector"]
    healthcheck:
      test: pg_isready -U postgres
      start_period: 10s
      timeout: 1s
      retries: 5
      interval: 60s
      start_interval: 1s
