{"cells": [{"cell_type": "markdown", "id": "100c0c81-6a9f-4ba1-b1a8-42aae82b7172", "metadata": {}, "source": ["# How to integrate LangGraph with AutoGen, CrewAI, and other frameworks\n", "\n", "LangGraph is a framework for building agentic and multi-agent applications. LangGraph can be easily integrated with other agent frameworks. \n", "\n", "The primary reasons you might want to integrate LangGraph with other agent frameworks:\n", "\n", "- create [multi-agent systems](../../concepts/multi_agent) where individual agents are built with different frameworks\n", "- leverage LangGraph to add features like [persistence](../../concepts/persistence), [streaming](../../concepts/streaming), [short and long-term memory](../../concepts/memory) and more\n", "\n", "The simplest way to integrate agents from other frameworks is by calling those agents inside a LangGraph [node](../../concepts/low_level/#nodes):\n", "\n", "```python\n", "from langgraph.graph import StateGraph, MessagesState, START\n", "\n", "autogen_agent = autogen.AssistantAgent(name=\"assistant\", ...)\n", "user_proxy = autogen.UserProxyAgent(name=\"user_proxy\", ...)\n", "\n", "def call_autogen_agent(state: MessagesState):\n", "    response = user_proxy.initiate_chat(\n", "        autogen_agent,\n", "        message=state[\"messages\"][-1],\n", "        ...\n", "    )\n", "    ...\n", "\n", "graph = (\n", "    StateGraph(MessagesState)\n", "    .add_node(call_autogen_agent)\n", "    .add_edge(START, \"call_autogen_agent\")\n", "    .compile()\n", ")\n", "\n", "graph.invoke({\n", "    \"messages\": [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"Find numbers between 10 and 30 in fi<PERSON><PERSON><PERSON> sequence\",\n", "        }\n", "    ]\n", "})\n", "```\n", "\n", "In this guide we show how to build a LangGraph chatbot that integrates with AutoGen, but you can follow the same approach with other frameworks."]}, {"cell_type": "markdown", "id": "b189ceb2-132b-4c7b-81b4-c7b8b062f833", "metadata": {}, "source": ["## Setup"]}, {"cell_type": "code", "execution_count": 1, "id": "62417d3a-94f9-4a52-9962-12639d714966", "metadata": {}, "outputs": [], "source": ["%pip install autogen langgraph"]}, {"cell_type": "code", "execution_count": 2, "id": "d46da41d-0a71-4654-aec8-9e6ad8765236", "metadata": {}, "outputs": [{"name": "stdin", "output_type": "stream", "text": ["OPENAI_API_KEY:  ········\n"]}], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "\n", "_set_env(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "id": "1926bbc3-6b06-41e0-9604-860a2bbf8fa3", "metadata": {}, "source": ["## Define AutoGen agent\n", "\n", "Here we define our AutoGen agent. Adapted from official tutorial [here](https://github.com/microsoft/autogen/blob/0.2/notebook/agentchat_web_info.ipynb)."]}, {"cell_type": "code", "execution_count": 3, "id": "524de117-ff09-4b26-bfe8-a9f85a46ffd5", "metadata": {}, "outputs": [], "source": ["import autogen\n", "import os\n", "\n", "config_list = [{\"model\": \"gpt-4o\", \"api_key\": os.environ[\"OPENAI_API_KEY\"]}]\n", "\n", "llm_config = {\n", "    \"timeout\": 600,\n", "    \"cache_seed\": 42,\n", "    \"config_list\": config_list,\n", "    \"temperature\": 0,\n", "}\n", "\n", "autogen_agent = autogen.AssistantAgent(\n", "    name=\"assistant\",\n", "    llm_config=llm_config,\n", ")\n", "\n", "user_proxy = autogen.UserProxyAgent(\n", "    name=\"user_proxy\",\n", "    human_input_mode=\"NEVER\",\n", "    max_consecutive_auto_reply=10,\n", "    is_termination_msg=lambda x: x.get(\"content\", \"\").rstrip().endswith(\"TERMINATE\"),\n", "    code_execution_config={\n", "        \"work_dir\": \"web\",\n", "        \"use_docker\": <PERSON><PERSON><PERSON>,\n", "    },  # Please set use_docker=True if docker is available to run the generated code. Using docker is safer than running the generated code directly.\n", "    llm_config=llm_config,\n", "    system_message=\"Reply TERMINATE if the task has been solved at full satisfaction. Otherwise, reply CONTINUE, or the reason why the task is not solved yet.\",\n", ")"]}, {"cell_type": "markdown", "id": "8aa858e2-4acb-4f75-be20-b9ccbbcb5073", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "dcc478f5-4a35-43f8-bf59-9cb71289cd00", "metadata": {}, "source": ["## Create the graph\n", "\n", "We will now create a LangGraph chatbot graph that calls AutoGen agent."]}, {"cell_type": "code", "execution_count": 4, "id": "d129e4e1-3766-429a-b806-cde3d8bc0469", "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import convert_to_openai_messages\n", "from langgraph.graph import StateGraph, MessagesState, START\n", "from langgraph.checkpoint.memory import MemorySaver\n", "\n", "\n", "def call_autogen_agent(state: MessagesState):\n", "    # convert to openai-style messages\n", "    messages = convert_to_openai_messages(state[\"messages\"])\n", "    response = user_proxy.initiate_chat(\n", "        autogen_agent,\n", "        message=messages[-1],\n", "        # pass previous message history as context\n", "        carryover=messages[:-1],\n", "    )\n", "    # get the final response from the agent\n", "    content = response.chat_history[-1][\"content\"]\n", "    return {\"messages\": {\"role\": \"assistant\", \"content\": content}}\n", "\n", "\n", "# add short-term memory for storing conversation history\n", "checkpointer = MemorySaver()\n", "\n", "builder = StateGraph(MessagesState)\n", "builder.add_node(call_autogen_agent)\n", "builder.add_edge(START, \"call_autogen_agent\")\n", "graph = builder.compile(checkpointer=checkpointer)"]}, {"cell_type": "code", "execution_count": 5, "id": "c761fc05-e8b6-4905-a793-eb7522d20060", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, Image\n", "\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "23d629c3-1d6b-40af-adf6-915e15657566", "metadata": {}, "source": ["## Run the graph\n", "\n", "We can now run the graph."]}, {"cell_type": "code", "execution_count": 6, "id": "a279b667-0f5d-4008-8d43-c806a3f379c4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33muser_proxy\u001b[0m (to assistant):\n", "\n", "Find numbers between 10 and 30 in <PERSON><PERSON><PERSON><PERSON> sequence\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33massistant\u001b[0m (to user_proxy):\n", "\n", "To find numbers between 10 and 30 in the <PERSON><PERSON><PERSON><PERSON> sequence, we can generate the <PERSON><PERSON><PERSON><PERSON> sequence and check which numbers fall within this range. Here's a plan:\n", "\n", "1. Generate <PERSON> numbers starting from 0.\n", "2. Continue generating until the numbers exceed 30.\n", "3. Collect and print the numbers that are between 10 and 30.\n", "\n", "Let's implement this in Python:\n", "\n", "```python\n", "# filename: fibonacci_range.py\n", "\n", "def fibonacci_sequence():\n", "    a, b = 0, 1\n", "    while a <= 30:\n", "        if 10 <= a <= 30:\n", "            print(a)\n", "        a, b = b, a + b\n", "\n", "fibonacci_sequence()\n", "```\n", "\n", "This script will print the <PERSON><PERSON><PERSON><PERSON> numbers between 10 and 30. Please execute the code to see the result.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> EXECUTING CODE BLOCK 0 (inferred language is python)...\u001b[0m\n", "\u001b[33muser_proxy\u001b[0m (to assistant):\n", "\n", "exitcode: 0 (execution succeeded)\n", "Code output: \n", "13\n", "21\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33massistant\u001b[0m (to user_proxy):\n", "\n", "The Fibonacci numbers between 10 and 30 are 13 and 21. \n", "\n", "These numbers are part of the <PERSON><PERSON><PERSON><PERSON> sequence, which is generated by adding the two preceding numbers to get the next number, starting from 0 and 1. \n", "\n", "The sequence goes: 0, 1, 1, 2, 3, 5, 8, 13, 21, 34, ...\n", "\n", "As you can see, 13 and 21 are the only numbers in this sequence that fall between 10 and 30.\n", "\n", "TERMINATE\n", "\n", "--------------------------------------------------------------------------------\n", "{'call_autogen_agent': {'messages': {'role': 'assistant', 'content': 'The Fibonacci numbers between 10 and 30 are 13 and 21. \\n\\nThese numbers are part of the <PERSON><PERSON><PERSON><PERSON> sequence, which is generated by adding the two preceding numbers to get the next number, starting from 0 and 1. \\n\\nThe sequence goes: 0, 1, 1, 2, 3, 5, 8, 13, 21, 34, ...\\n\\nAs you can see, 13 and 21 are the only numbers in this sequence that fall between 10 and 30.\\n\\nTERMINATE'}}}\n"]}], "source": ["# pass the thread ID to persist agent outputs for future interactions\n", "# highlight-next-line\n", "config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "\n", "for chunk in graph.stream(\n", "    {\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"Find numbers between 10 and 30 in fi<PERSON><PERSON><PERSON> sequence\",\n", "            }\n", "        ]\n", "    },\n", "    # highlight-next-line\n", "    config,\n", "):\n", "    print(chunk)"]}, {"cell_type": "markdown", "id": "c6cd57b4-d4ee-49f6-be12-318613849669", "metadata": {}, "source": ["Since we're leveraging LangGraph's [persistence](https://langchain-ai.github.io/langgraph/concepts/persistence/) features we can now continue the conversation using the same thread ID -- LangGraph will automatically pass previous history to the AutoGen agent:"]}, {"cell_type": "code", "execution_count": 7, "id": "e68811a7-962e-4fe3-9f45-9b99ebbe04e7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33muser_proxy\u001b[0m (to assistant):\n", "\n", "Multiply the last number by 3\n", "Context: \n", "Find numbers between 10 and 30 in <PERSON><PERSON><PERSON><PERSON> sequence\n", "The Fibonacci numbers between 10 and 30 are 13 and 21. \n", "\n", "These numbers are part of the <PERSON><PERSON><PERSON><PERSON> sequence, which is generated by adding the two preceding numbers to get the next number, starting from 0 and 1. \n", "\n", "The sequence goes: 0, 1, 1, 2, 3, 5, 8, 13, 21, 34, ...\n", "\n", "As you can see, 13 and 21 are the only numbers in this sequence that fall between 10 and 30.\n", "\n", "TERMINATE\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33massistant\u001b[0m (to user_proxy):\n", "\n", "The last number in the <PERSON><PERSON><PERSON><PERSON> sequence between 10 and 30 is 21. Multiplying 21 by 3 gives:\n", "\n", "21 * 3 = 63\n", "\n", "TERMINATE\n", "\n", "--------------------------------------------------------------------------------\n", "{'call_autogen_agent': {'messages': {'role': 'assistant', 'content': 'The last number in the <PERSON><PERSON><PERSON><PERSON> sequence between 10 and 30 is 21. Multiplying 21 by 3 gives:\\n\\n21 * 3 = 63\\n\\nTERMINATE'}}}\n"]}], "source": ["for chunk in graph.stream(\n", "    {\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"Multiply the last number by 3\",\n", "            }\n", "        ]\n", "    },\n", "    # highlight-next-line\n", "    config,\n", "):\n", "    print(chunk)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}