{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to pass custom run ID or set tags and metadata for graph runs in LangSmith\n", "\n", "<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Prerequisites</p>\n", "    <p>\n", "        This guide assumes familiarity with the following:\n", "        <ul>\n", "            <li>\n", "                <a href=\"https://docs.smith.langchain.com\">\n", "                    LangSmith Documentation\n", "                </a>\n", "            </li>\n", "            <li>\n", "                <a href=\"https://smith.langchain.com\">\n", "                    LangSmith Platform\n", "                </a>\n", "            </li>\n", "            <li>\n", "                <a href=\"https://api.python.langchain.com/en/latest/runnables/langchain_core.runnables.config.RunnableConfig.html#langchain_core.runnables.config.RunnableConfig\">\n", "                    RunnableConfig\n", "                </a>\n", "            </li>\n", "            <li>\n", "                <a href=\"https://docs.smith.langchain.com/how_to_guides/tracing/trace_with_langchain#add-metadata-and-tags-to-traces\">\n", "                    Add metadata and tags to traces\n", "                </a>                \n", "            </li>\n", "            <li>\n", "                <a href=\"https://docs.smith.langchain.com/how_to_guides/tracing/trace_with_langchain#customize-run-name\">\n", "                    Customize run name\n", "                </a>                \n", "            </li>\n", "        </ul>\n", "    </p>\n", "</div> \n", "\n", "Debugging graph runs can sometimes be difficult to do in an IDE or terminal. [LangSmith](https://docs.smith.langchain.com) lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read the [<PERSON><PERSON><PERSON> documentation](https://docs.smith.langchain.com) for more information on how to get started.\n", "\n", "To make it easier to identify and analyzed traces generated during graph invocation, you can set additional configuration at run time (see [RunnableConfig](https://api.python.langchain.com/en/latest/runnables/langchain_core.runnables.config.RunnableConfig.html#langchain_core.runnables.config.RunnableConfig)):\n", "\n", "| **Field**   | **Type**            | **Description**                                                                                                    |\n", "|-------------|---------------------|--------------------------------------------------------------------------------------------------------------------|\n", "| run_name    | `str`               | Name for the tracer run for this call. Defaults to the name of the class.                                          |\n", "| run_id      | `UUID`              | Unique identifier for the tracer run for this call. If not provided, a new UUID will be generated.                 |\n", "| tags        | `List[str]`         | Tags for this call and any sub-calls (e.g., a Chain calling an LLM). You can use these to filter calls.            |\n", "| metadata    | `Dict[str, Any]`    | Metadata for this call and any sub-calls (e.g., a Chain calling an LLM). Keys should be strings, values should be JSON-serializable. |\n", "\n", "LangGraph graphs implement the [LangChain Runnable Interface](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.Runnable.html) and accept a second argument (`RunnableConfig`) in methods like `invoke`, `ainvoke`, `stream` etc.\n", "\n", "The LangSmith platform will allow you to search and filter traces based on `run_name`, `run_id`, `tags` and `metadata`.\n", "\n", "\n", "## TLDR\n", "\n", "```python\n", "import uuid\n", "# Generate a random UUID -- it must be a UUID\n", "config = {\"run_id\": uuid.uuid4()}, \"tags\": [\"my_tag1\"], \"metadata\": {\"a\": 5}}\n", "# Works with all standard Runnable methods \n", "# like invoke, batch, ainvoke, astream_events etc\n", "graph.stream(inputs, config, stream_mode=\"values\")\n", "```\n", "\n", "The rest of the how to guide will show a full agent.\n", "\n", "## Setup\n", "\n", "First, let's install the required packages and set our API keys"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install --quiet -U langgraph langchain_openai"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "\n", "_set_env(\"OPENAI_API_KEY\")\n", "_set_env(\"LANGSMITH_API_KEY\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define the graph\n", "\n", "For this example we will use the [prebuilt ReAct agent](https://langchain-ai.github.io/langgraph/how-tos/create-react-agent/)."]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "from typing import Literal\n", "from langgraph.prebuilt import create_react_agent\n", "from langchain_core.tools import tool\n", "\n", "# First we initialize the model we want to use.\n", "model = ChatOpenAI(model=\"gpt-4o\", temperature=0)\n", "\n", "\n", "# For this tutorial we will use custom tool that returns pre-defined values for weather in two cities (NYC & SF)\n", "@tool\n", "def get_weather(city: Literal[\"nyc\", \"sf\"]):\n", "    \"\"\"Use this to get weather information.\"\"\"\n", "    if city == \"nyc\":\n", "        return \"It might be cloudy in nyc\"\n", "    elif city == \"sf\":\n", "        return \"It's always sunny in sf\"\n", "    else:\n", "        raise AssertionError(\"Unknown city\")\n", "\n", "\n", "tools = [get_weather]\n", "\n", "\n", "# Define the graph\n", "graph = create_react_agent(model, tools=tools)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run your graph\n", "\n", "Now that we've defined our graph let's run it once and view the trace in LangSmith. In order for our trace to be easily accessible in LangSmith, we will pass in a custom `run_id` in the config.\n", "\n", "This assumes that you have set your `LANGSMITH_API_KEY` environment variable.\n", "\n", "Note that you can also configure what project to trace to by setting the `LANGCHAIN_PROJECT` environment variable, by default runs will be traced to the `default` project."]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "what is the weather in sf\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  get_weather (call_9ZudXyMAdlUjptq9oMGtQo8o)\n", " Call ID: call_9ZudXyMAdlUjptq9oMGtQo8o\n", "  Args:\n", "    city: sf\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: get_weather\n", "\n", "It's always sunny in sf\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "The weather in San Francisco is currently sunny.\n"]}], "source": ["import uuid\n", "\n", "\n", "def print_stream(stream):\n", "    for s in stream:\n", "        message = s[\"messages\"][-1]\n", "        if isinstance(message, tuple):\n", "            print(message)\n", "        else:\n", "            message.pretty_print()\n", "\n", "\n", "inputs = {\"messages\": [(\"user\", \"what is the weather in sf\")]}\n", "\n", "config = {\"run_name\": \"agent_007\", \"tags\": [\"cats are awesome\"]}\n", "\n", "print_stream(graph.stream(inputs, config, stream_mode=\"values\"))"]}, {"attachments": {"d38d1f2b-0f4c-4707-b531-a3c749de987f.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["## View the trace in LangSmith\n", "\n", "Now that we've ran our graph, let's head over to LangSmith and view our trace. First click into the project that you traced to (in our case the default project). You should see a run with the custom run name \"agent_007\".\n", "\n", "![image.png](attachment:d38d1f2b-0f4c-4707-b531-a3c749de987f.png)"]}, {"attachments": {"410e0089-2ab8-46bb-a61a-827187fd46b3.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["In addition, you will be able to filter traces after the fact using the tags or metadata provided. For example,\n", "\n", "![image.png](attachment:410e0089-2ab8-46bb-a61a-827187fd46b3.png)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 4}