{"cells": [{"cell_type": "markdown", "id": "8381b6e0-29a6-48c5-b451-5d2549351249", "metadata": {}, "source": ["# How to use LangGraph Platform to deploy CrewAI, AutoGen, and other frameworks\n", "\n", "[LangGraph Platform](https://langchain-ai.github.io/langgraph/concepts/langgraph_platform/) provides infrastructure for deploying agents. This integrates seamlessly with LangGraph, but can also work with other frameworks. The way to make this work is to wrap the agent in a single LangGraph node, and have that be the entire graph.\n", "\n", "Doing so will allow you to deploy to LangGraph Platform, and allows you to get a lot of the [benefits](https://langchain-ai.github.io/langgraph/concepts/langgraph_platform/). You get horizontally scalable infrastructure, a task queue to handle bursty operations, a persistence layer to power short term memory, and long term memory support.\n", "\n", "In this guide we show how to do this with an AutoGen agent, but this method should work for agents defined in other frameworks like CrewAI, LlamaIndex, and others as well."]}, {"cell_type": "markdown", "id": "1113cb16-b538-448c-924c-85731ce96ebd", "metadata": {}, "source": ["## Setup"]}, {"cell_type": "code", "execution_count": 10, "id": "f05993fa-9d03-4f45-bc13-0a8d87260d86", "metadata": {"scrolled": true}, "outputs": [], "source": ["%pip install autogen langgraph"]}, {"cell_type": "code", "execution_count": null, "id": "f4e0ca12-1714-4776-a30a-9527e519799b", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "\n", "_set_env(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "id": "1926bbc3-6b06-41e0-9604-860a2bbf8fa3", "metadata": {}, "source": ["## Define autogen agent\n", "\n", "Here we define our AutoGen agent. From https://github.com/microsoft/autogen/blob/0.2/notebook/agentchat_web_info.ipynb"]}, {"cell_type": "code", "execution_count": null, "id": "d4a14dc7-d565-4207-8788-525f85b9fb27", "metadata": {}, "outputs": [], "source": ["import autogen\n", "import os\n", "\n", "config_list = [{\"model\": \"gpt-4o\", \"api_key\": os.environ[\"OPENAI_API_KEY\"]}]\n", "\n", "llm_config = {\n", "    \"timeout\": 600,\n", "    \"cache_seed\": 42,\n", "    \"config_list\": config_list,\n", "    \"temperature\": 0,\n", "}\n", "\n", "autogen_agent = autogen.AssistantAgent(\n", "    name=\"assistant\",\n", "    llm_config=llm_config,\n", ")\n", "\n", "user_proxy = autogen.UserProxyAgent(\n", "    name=\"user_proxy\",\n", "    human_input_mode=\"NEVER\",\n", "    max_consecutive_auto_reply=10,\n", "    is_termination_msg=lambda x: x.get(\"content\", \"\").rstrip().endswith(\"TERMINATE\"),\n", "    code_execution_config={\n", "        \"work_dir\": \"web\",\n", "        \"use_docker\": <PERSON><PERSON><PERSON>,\n", "    },  # Please set use_docker=True if docker is available to run the generated code. Using docker is safer than running the generated code directly.\n", "    llm_config=llm_config,\n", "    system_message=\"Reply TERMINATE if the task has been solved at full satisfaction. Otherwise, reply CONTINUE, or the reason why the task is not solved yet.\",\n", ")"]}, {"cell_type": "markdown", "id": "b1170836-f23e-4e4c-ab83-ce791cd7fbd2", "metadata": {}, "source": ["## Wrap in LangGraph\n", "\n", "We now wrap the AutoGen agent in a single LangGraph node, and make that the entire graph.\n", "The main thing this involves is defining an Input and Output schema for the node, which you would need to do if deploying this manually, so it's no extra work"]}, {"cell_type": "code", "execution_count": 11, "id": "7b417c16-ff4e-4d5c-a9a9-0aaeeef6ede5", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, MessagesState\n", "\n", "\n", "def call_autogen_agent(state: MessagesState):\n", "    last_message = state[\"messages\"][-1]\n", "    response = user_proxy.initiate_chat(autogen_agent, message=last_message.content)\n", "    # get the final response from the agent\n", "    content = response.chat_history[-1][\"content\"]\n", "    return {\"messages\": {\"role\": \"assistant\", \"content\": content}}\n", "\n", "\n", "graph = StateGraph(MessagesState)\n", "graph.add_node(call_autogen_agent)\n", "graph.set_entry_point(\"call_autogen_agent\")\n", "graph = graph.compile()"]}, {"cell_type": "markdown", "id": "f6a18377-ac29-478f-a76a-b213f1a3c85d", "metadata": {}, "source": ["## Deploy with LangGraph Platform\n", "\n", "You can now deploy this as you normally would with LangGraph Platform. See [these instructions](https://langchain-ai.github.io/langgraph/concepts/deployment_options/) for more details."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}