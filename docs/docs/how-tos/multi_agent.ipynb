{"cells": [{"cell_type": "markdown", "id": "34d3d54e-9a2b-481e-bccd-74aca7a53f9a", "metadata": {}, "source": ["# Build multi-agent systems"]}, {"attachments": {}, "cell_type": "markdown", "id": "3f0b4f70-f14e-4026-82c0-874786789ee8", "metadata": {}, "source": ["A single agent might struggle if it needs to specialize in multiple domains or manage many tools. To tackle this, you can break your agent into smaller, independent agents and composing them into a [multi-agent system](../../concepts/multi_agent).\n", "\n", "In multi-agent systems, agents need to communicate between each other. They do so via [handoffs](#handoffs) — a primitive that describes which agent to hand control to and the payload to send to that agent.\n", "\n", "This guide covers the following:\n", "\n", "* implementing [handoffs](#handoffs) between agents\n", "* using handoffs and the prebuilt [agent](../../agents/agents) to [build a custom multi-agent system](#build-a-multi-agent-system)\n", "\n", "To get started with building multi-agent systems, check out LangGraph [prebuilt implementations](#prebuilt-implementations) of two of the most popular multi-agent architectures — [supervisor](../../agents/multi-agent#supervisor) and [swarm](../../agents/multi-agent#swarm)."]}, {"attachments": {}, "cell_type": "markdown", "id": "7d43e110-16fc-4899-97f1-015d5b804b87", "metadata": {}, "source": ["## Handoffs\n", "\n", "To set up communication between the agents in a multi-agent system you can use [**handoffs**](../../concepts/multi_agent#handoffs) — a pattern where one agent *hands off* control to another. Handoffs allow you to specify:\n", "\n", "- **destination**: target agent to navigate to (e.g., name of the LangGraph node to go to)\n", "- **payload**: information to pass to that agent (e.g., state update)\n", "\n", "### Create handoffs\n", "\n", "To implement handoffs, you can return [`Command`](../command) objects from your agent nodes or tools:\n", "\n", "```python\n", "from typing import Annotated\n", "from langchain_core.tools import tool, InjectedToolCallId\n", "from langgraph.prebuilt import create_react_agent, InjectedState\n", "from langgraph.graph import StateGraph, START, MessagesState\n", "from langgraph.types import Command\n", "\n", "def create_handoff_tool(*, agent_name: str, description: str | None = None):\n", "    name = f\"transfer_to_{agent_name}\"\n", "    description = description or f\"Transfer to {agent_name}\"\n", "\n", "    @tool(name, description=description)\n", "    def handoff_tool(\n", "        # highlight-next-line\n", "        state: Annotated[MessagesState, InjectedState], # (1)!\n", "        # highlight-next-line\n", "        tool_call_id: Annotated[str, InjectedToolCallId],\n", "    ) -> Command:\n", "        tool_message = {\n", "            \"role\": \"tool\",\n", "            \"content\": f\"Successfully transferred to {agent_name}\",\n", "            \"name\": name,\n", "            \"tool_call_id\": tool_call_id,\n", "        }\n", "        return Command(  # (2)!\n", "            # highlight-next-line\n", "            goto=agent_name,  # (3)!\n", "            # highlight-next-line\n", "            update={\"messages\": state[\"messages\"] + [tool_message]},  # (4)!\n", "            # highlight-next-line\n", "            graph=Command.PARENT,  # (5)!\n", "        )\n", "    return handoff_tool\n", "```\n", "\n", "1. Access the [state](../../concepts/low_level#state) of the agent that is calling the handoff tool using the [InjectedState][langgraph.prebuilt.InjectedState] annotation. See [this guide](../tool-calling/#read-state) for more information.\n", "2. The `Command` primitive allows specifying a state update and a node transition as a single operation, making it useful for implementing handoffs.\n", "3. Name of the agent or node to hand off to.\n", "4. Take the agent's messages and **add** them to the parent's **state** as part of the handoff. The next agent will see the parent state.\n", "5. Indicate to <PERSON>G<PERSON><PERSON> that we need to navigate to agent node in a **parent** multi-agent graph.\n", "\n", "!!! tip\n", "\n", "    If you want to use tools that return `Command`, you can either use prebuilt [`create_react_agent`][langgraph.prebuilt.chat_agent_executor.create_react_agent] / [`ToolNode`][langgraph.prebuilt.tool_node.ToolNode] components, or implement your own tool-executing node that collects `Command` objects returned by the tools and returns a list of them, e.g.:\n", "    \n", "    ```python\n", "    def call_tools(state):\n", "        ...\n", "        commands = [tools_by_name[tool_call[\"name\"]].invoke(tool_call) for tool_call in tool_calls]\n", "        return commands\n", "    ```\n", "\n", "!!! Important\n", "\n", "    This handoff implementation assumes that:\n", "    \n", "      - each agent receives overall message history (across all agents) in the multi-agent system as its input. If you want more control over agent inputs, see [this section](#control-agent-inputs)\n", "      - each agent outputs its internal messages history to the overall message history of the multi-agent system. If you want more control over **how agent outputs are added**, wrap the agent in a separate node function:\n", "\n", "        ```python\n", "        def call_hotel_assistant(state):\n", "            # return agent's final response,\n", "            # excluding inner monologue\n", "            response = hotel_assistant.invoke(state)\n", "            # highlight-next-line\n", "            return {\"messages\": response[\"messages\"][-1]}\n", "        ```"]}, {"cell_type": "markdown", "id": "3956f12d-285a-4799-a0a5-db13def58a15", "metadata": {}, "source": ["### Control agent inputs\n", "\n", "You can use the [`Send()`][langgraph.types.Send] primitive to directly send data to the worker agents during the handoff. For example, you can request that the calling agent populate a task description for the next agent:\n", "\n", "```python\n", "\n", "from typing import Annotated\n", "from langchain_core.tools import tool, InjectedToolCallId\n", "from langgraph.prebuilt import InjectedState\n", "from langgraph.graph import StateGraph, START, MessagesState\n", "# highlight-next-line\n", "from langgraph.types import Command, Send\n", "\n", "def create_task_description_handoff_tool(\n", "    *, agent_name: str, description: str | None = None\n", "):\n", "    name = f\"transfer_to_{agent_name}\"\n", "    description = description or f\"Ask {agent_name} for help.\"\n", "\n", "    @tool(name, description=description)\n", "    def handoff_tool(\n", "        # this is populated by the calling agent\n", "        task_description: Annotated[\n", "            str,\n", "            \"Description of what the next agent should do, including all of the relevant context.\",\n", "        ],\n", "        # these parameters are ignored by the LLM\n", "        state: Annotated[MessagesState, InjectedState],\n", "    ) -> Command:\n", "        task_description_message = {\"role\": \"user\", \"content\": task_description}\n", "        agent_input = {**state, \"messages\": [task_description_message]}\n", "        return Command(\n", "            # highlight-next-line\n", "            goto=[Send(agent_name, agent_input)],\n", "            graph=Command.PARENT,\n", "        )\n", "\n", "    return handoff_tool\n", "```\n", "\n", "See the multi-agent [supervisor](../tutorials/agent_supervisor.ipynb#4-create-delegation-tasks) tutorial for a full example of using [`Send()`][langgraph.types.Send] in handoffs."]}, {"attachments": {}, "cell_type": "markdown", "id": "21511f57-7bf3-4223-9a17-ce9fc84c40ab", "metadata": {}, "source": ["## Build a multi-agent system\n", "\n", "You can use handoffs in any agents built with LangGraph. We recommend using the prebuilt [agent](../../agents/overview) or [`ToolNode`](../tool-calling#use-prebuilt-toolnode), as they natively support handoffs tools returning `Command`. Below is an example of how you can implement a multi-agent system for booking travel using handoffs:\n", "\n", "```python\n", "from langgraph.prebuilt import create_react_agent\n", "from langgraph.graph import StateGraph, START, MessagesState\n", "\n", "def create_handoff_tool(*, agent_name: str, description: str | None = None):\n", "    # same implementation as above\n", "    ...\n", "    return Command(...)\n", "\n", "# Handoffs\n", "transfer_to_hotel_assistant = create_handoff_tool(agent_name=\"hotel_assistant\")\n", "transfer_to_flight_assistant = create_handoff_tool(agent_name=\"flight_assistant\")\n", "\n", "# Define agents\n", "flight_assistant = create_react_agent(\n", "    model=\"anthropic:claude-3-5-sonnet-latest\",\n", "    # highlight-next-line\n", "    tools=[..., transfer_to_hotel_assistant],\n", "    # highlight-next-line\n", "    name=\"flight_assistant\"\n", ")\n", "hotel_assistant = create_react_agent(\n", "    model=\"anthropic:claude-3-5-sonnet-latest\",\n", "    # highlight-next-line\n", "    tools=[..., transfer_to_flight_assistant],\n", "    # highlight-next-line\n", "    name=\"hotel_assistant\"\n", ")\n", "\n", "# Define multi-agent graph\n", "multi_agent_graph = (\n", "    StateGraph(MessagesState)\n", "    # highlight-next-line\n", "    .add_node(flight_assistant)\n", "    # highlight-next-line\n", "    .add_node(hotel_assistant)\n", "    .add_edge(STAR<PERSON>, \"flight_assistant\")\n", "    .compile()\n", ")\n", "```\n", "\n", "??? example \"Full example: Multi-agent system for booking travel\"\n", "\n", "    ```python\n", "    from typing import Annotated\n", "    from langchain_core.messages import convert_to_messages\n", "    from langchain_core.tools import tool, InjectedToolCallId\n", "    from langgraph.prebuilt import create_react_agent, InjectedState\n", "    from langgraph.graph import StateGraph, START, MessagesState\n", "    from langgraph.types import Command\n", "    \n", "    # We'll use `pretty_print_messages` helper to render the streamed agent outputs nicely\n", "    \n", "    def pretty_print_message(message, indent=False):\n", "        pretty_message = message.pretty_repr(html=True)\n", "        if not indent:\n", "            print(pretty_message)\n", "            return\n", "    \n", "        indented = \"\\n\".join(\"\\t\" + c for c in pretty_message.split(\"\\n\"))\n", "        print(indented)\n", "    \n", "    \n", "    def pretty_print_messages(update, last_message=False):\n", "        is_subgraph = False\n", "        if isinstance(update, tuple):\n", "            ns, update = update\n", "            # skip parent graph updates in the printouts\n", "            if len(ns) == 0:\n", "                return\n", "    \n", "            graph_id = ns[-1].split(\":\")[0]\n", "            print(f\"Update from subgraph {graph_id}:\")\n", "            print(\"\\n\")\n", "            is_subgraph = True\n", "    \n", "        for node_name, node_update in update.items():\n", "            update_label = f\"Update from node {node_name}:\"\n", "            if is_subgraph:\n", "                update_label = \"\\t\" + update_label\n", "    \n", "            print(update_label)\n", "            print(\"\\n\")\n", "    \n", "            messages = convert_to_messages(node_update[\"messages\"])\n", "            if last_message:\n", "                messages = messages[-1:]\n", "    \n", "            for m in messages:\n", "                pretty_print_message(m, indent=is_subgraph)\n", "            print(\"\\n\")\n", "\n", "\n", "    def create_handoff_tool(*, agent_name: str, description: str | None = None):\n", "        name = f\"transfer_to_{agent_name}\"\n", "        description = description or f\"Transfer to {agent_name}\"\n", "    \n", "        @tool(name, description=description)\n", "        def handoff_tool(\n", "            # highlight-next-line\n", "            state: Annotated[MessagesState, InjectedState], # (1)!\n", "            # highlight-next-line\n", "            tool_call_id: Annotated[str, InjectedToolCallId],\n", "        ) -> Command:\n", "            tool_message = {\n", "                \"role\": \"tool\",\n", "                \"content\": f\"Successfully transferred to {agent_name}\",\n", "                \"name\": name,\n", "                \"tool_call_id\": tool_call_id,\n", "            }\n", "            return Command(  # (2)!\n", "                # highlight-next-line\n", "                goto=agent_name,  # (3)!\n", "                # highlight-next-line\n", "                update={\"messages\": state[\"messages\"] + [tool_message]},  # (4)!\n", "                # highlight-next-line\n", "                graph=Command.PARENT,  # (5)!\n", "            )\n", "        return handoff_tool\n", "    \n", "    # Handoffs\n", "    transfer_to_hotel_assistant = create_handoff_tool(\n", "        agent_name=\"hotel_assistant\",\n", "        description=\"Transfer user to the hotel-booking assistant.\",\n", "    )\n", "    transfer_to_flight_assistant = create_handoff_tool(\n", "        agent_name=\"flight_assistant\",\n", "        description=\"Transfer user to the flight-booking assistant.\",\n", "    )\n", "    \n", "    # Simple agent tools\n", "    def book_hotel(hotel_name: str):\n", "        \"\"\"Book a hotel\"\"\"\n", "        return f\"Successfully booked a stay at {hotel_name}.\"\n", "    \n", "    def book_flight(from_airport: str, to_airport: str):\n", "        \"\"\"Book a flight\"\"\"\n", "        return f\"Successfully booked a flight from {from_airport} to {to_airport}.\"\n", "    \n", "    # Define agents\n", "    flight_assistant = create_react_agent(\n", "        model=\"anthropic:claude-3-5-sonnet-latest\",\n", "        # highlight-next-line\n", "        tools=[book_flight, transfer_to_hotel_assistant],\n", "        prompt=\"You are a flight booking assistant\",\n", "        # highlight-next-line\n", "        name=\"flight_assistant\"\n", "    )\n", "    hotel_assistant = create_react_agent(\n", "        model=\"anthropic:claude-3-5-sonnet-latest\",\n", "        # highlight-next-line\n", "        tools=[book_hotel, transfer_to_flight_assistant],\n", "        prompt=\"You are a hotel booking assistant\",\n", "        # highlight-next-line\n", "        name=\"hotel_assistant\"\n", "    )\n", "    \n", "    # Define multi-agent graph\n", "    multi_agent_graph = (\n", "        StateGraph(MessagesState)\n", "        .add_node(flight_assistant)\n", "        .add_node(hotel_assistant)\n", "        .add_edge(STAR<PERSON>, \"flight_assistant\")\n", "        .compile()\n", "    )\n", "    \n", "    # Run the multi-agent graph\n", "    for chunk in multi_agent_graph.stream(\n", "        {\n", "            \"messages\": [\n", "                {\n", "                    \"role\": \"user\",\n", "                    \"content\": \"book a flight from BOS to JFK and a stay at McKittrick Hotel\"\n", "                }\n", "            ]\n", "        },\n", "        # highlight-next-line\n", "        subgraphs=True\n", "    ):\n", "        pretty_print_messages(chunk)\n", "    ```\n", "\n", "    1. Access agent's state\n", "    2. The `Command` primitive allows specifying a state update and a node transition as a single operation, making it useful for implementing handoffs.\n", "    3. Name of the agent or node to hand off to.\n", "    4. Take the agent's messages and **add** them to the parent's **state** as part of the handoff. The next agent will see the parent state.\n", "    5. Indicate to <PERSON>G<PERSON><PERSON> that we need to navigate to agent node in a **parent** multi-agent graph."]}, {"cell_type": "markdown", "id": "e8314da4-9971-429b-9e70-58b40795de74", "metadata": {}, "source": ["## Multi-turn conversation\n", "\n", "Users might want to engage in a *multi-turn conversation* with one or more agents. To build a system that can handle this, you can create a node that uses an [`interrupt`][langgraph.types.interrupt] to collect user input and routes back to the **active** agent.\n", "\n", "The agents can then be implemented as nodes in a graph that executes agent steps and determines the next action:\n", "\n", "1. **Wait for user input** to continue the conversation, or  \n", "2. **Route to another agent** (or back to itself, such as in a loop) via a [handoff](#handoffs)\n", "\n", "```python\n", "def human(state) -> Command[Literal[\"agent\", \"another_agent\"]]:\n", "    \"\"\"A node for collecting user input.\"\"\"\n", "    user_input = interrupt(value=\"Ready for user input.\")\n", "\n", "    # Determine the active agent.\n", "    active_agent = ...\n", "\n", "    ...\n", "    return Command(\n", "        update={\n", "            \"messages\": [{\n", "                \"role\": \"human\",\n", "                \"content\": user_input,\n", "            }]\n", "        },\n", "        goto=active_agent\n", "    )\n", "\n", "def agent(state) -> Command[Literal[\"agent\", \"another_agent\", \"human\"]]:\n", "    # The condition for routing/halting can be anything, e.g. LLM tool call / structured output, etc.\n", "    goto = get_next_agent(...)  # 'agent' / 'another_agent'\n", "    if goto:\n", "        return Command(goto=goto, update={\"my_state_key\": \"my_state_value\"})\n", "    else:\n", "        return Command(goto=\"human\") # Go to human node\n", "```\n", "\n", "??? example \"Full example: multi-agent system for travel recommendations\"\n", "\n", "    In this example, we will build a team of travel assistant agents that can communicate with each other via handoffs.\n", "    \n", "    We will create 2 agents:\n", "    \n", "    * travel_advisor: can help with travel destination recommendations. Can ask hotel_advisor for help.\n", "    * hotel_advisor: can help with hotel recommendations. Can ask travel_advisor for help.\n", "\n", "    ```python\n", "    from langchain_anthropic import ChatAnthropic\n", "    from langgraph.graph import MessagesState, StateGraph, START\n", "    from langgraph.prebuilt import create_react_agent, InjectedState\n", "    from langgraph.types import Command, interrupt\n", "    from langgraph.checkpoint.memory import MemorySaver\n", "    \n", "    \n", "    model = ChatAnthropic(model=\"claude-3-5-sonnet-latest\")\n", "\n", "    class MultiAgentState(MessagesState):\n", "        last_active_agent: str\n", "    \n", "    \n", "    # Define travel advisor tools and ReAct agent\n", "    travel_advisor_tools = [\n", "        get_travel_recommendations,\n", "        make_handoff_tool(agent_name=\"hotel_advisor\"),\n", "    ]\n", "    travel_advisor = create_react_agent(\n", "        model,\n", "        travel_advisor_tools,\n", "        prompt=(\n", "            \"You are a general travel expert that can recommend travel destinations (e.g. countries, cities, etc). \"\n", "            \"If you need hotel recommendations, ask 'hotel_advisor' for help. \"\n", "            \"You MUST include human-readable response before transferring to another agent.\"\n", "        ),\n", "    )\n", "    \n", "    \n", "    def call_travel_advisor(\n", "        state: MultiAgentState,\n", "    ) -> Command[Literal[\"hotel_advisor\", \"human\"]]:\n", "        # You can also add additional logic like changing the input to the agent / output from the agent, etc.\n", "        # NOTE: we're invoking the ReAct agent with the full history of messages in the state\n", "        response = travel_advisor.invoke(state)\n", "        update = {**response, \"last_active_agent\": \"travel_advisor\"}\n", "        return Command(update=update, goto=\"human\")\n", "    \n", "    \n", "    # Define hotel advisor tools and ReAct agent\n", "    hotel_advisor_tools = [\n", "        get_hotel_recommendations,\n", "        make_handoff_tool(agent_name=\"travel_advisor\"),\n", "    ]\n", "    hotel_advisor = create_react_agent(\n", "        model,\n", "        hotel_advisor_tools,\n", "        prompt=(\n", "            \"You are a hotel expert that can provide hotel recommendations for a given destination. \"\n", "            \"If you need help picking travel destinations, ask 'travel_advisor' for help.\"\n", "            \"You MUST include human-readable response before transferring to another agent.\"\n", "        ),\n", "    )\n", "    \n", "    \n", "    def call_hotel_advisor(\n", "        state: MultiAgentState,\n", "    ) -> Command[Literal[\"travel_advisor\", \"human\"]]:\n", "        response = hotel_advisor.invoke(state)\n", "        update = {**response, \"last_active_agent\": \"hotel_advisor\"}\n", "        return Command(update=update, goto=\"human\")\n", "    \n", "    \n", "    def human_node(\n", "        state: MultiAgentState, config\n", "    ) -> Command[Literal[\"hotel_advisor\", \"travel_advisor\", \"human\"]]:\n", "        \"\"\"A node for collecting user input.\"\"\"\n", "    \n", "        user_input = interrupt(value=\"Ready for user input.\")\n", "        active_agent = state[\"last_active_agent\"]\n", "    \n", "        return Command(\n", "            update={\n", "                \"messages\": [\n", "                    {\n", "                        \"role\": \"human\",\n", "                        \"content\": user_input,\n", "                    }\n", "                ]\n", "            },\n", "            goto=active_agent,\n", "        )\n", "    \n", "    \n", "    builder = StateGraph(MultiAgentState)\n", "    builder.add_node(\"travel_advisor\", call_travel_advisor)\n", "    builder.add_node(\"hotel_advisor\", call_hotel_advisor)\n", "    \n", "    # This adds a node to collect human input, which will route\n", "    # back to the active agent.\n", "    builder.add_node(\"human\", human_node)\n", "    \n", "    # We'll always start with a general travel advisor.\n", "    builder.add_edge(START, \"travel_advisor\")\n", "    \n", "    \n", "    checkpointer = MemorySaver()\n", "    graph = builder.compile(checkpointer=checkpointer)\n", "    ```\n", "    \n", "    Let's test a multi turn conversation with this application.\n", "\n", "    ```python\n", "    import uuid\n", "    \n", "    thread_config = {\"configurable\": {\"thread_id\": str(uuid.uuid4())}}\n", "    \n", "    inputs = [\n", "        # 1st round of conversation,\n", "        {\n", "            \"messages\": [\n", "                {\"role\": \"user\", \"content\": \"i wanna go somewhere warm in the caribbean\"}\n", "            ]\n", "        },\n", "        # Since we're using `interrupt`, we'll need to resume using the Command primitive.\n", "        # 2nd round of conversation,\n", "        Command(\n", "            resume=\"could you recommend a nice hotel in one of the areas and tell me which area it is.\"\n", "        ),\n", "        # 3rd round of conversation,\n", "        Command(\n", "            resume=\"i like the first one. could you recommend something to do near the hotel?\"\n", "        ),\n", "    ]\n", "    \n", "    for idx, user_input in enumerate(inputs):\n", "        print()\n", "        print(f\"--- Conversation Turn {idx + 1} ---\")\n", "        print()\n", "        print(f\"User: {user_input}\")\n", "        print()\n", "        for update in graph.stream(\n", "            user_input,\n", "            config=thread_config,\n", "            stream_mode=\"updates\",\n", "        ):\n", "            for node_id, value in update.items():\n", "                if isinstance(value, dict) and value.get(\"messages\", []):\n", "                    last_message = value[\"messages\"][-1]\n", "                    if isinstance(last_message, dict) or last_message.type != \"ai\":\n", "                        continue\n", "                    print(f\"{node_id}: {last_message.content}\")\n", "    ```\n", "    \n", "    ```\n", "    --- Conversation Turn 1 ---\n", "    \n", "    User: {'messages': [{'role': 'user', 'content': 'i wanna go somewhere warm in the caribbean'}]}\n", "    \n", "    travel_advisor: Based on the recommendations, Aruba would be an excellent choice for your Caribbean getaway! Aruba is known as \"One Happy Island\" and offers:\n", "    - Year-round warm weather with consistent temperatures around 82°F (28°C)\n", "    - Beautiful white sand beaches like Eagle Beach and Palm Beach\n", "    - Clear turquoise waters perfect for swimming and snorkeling\n", "    - Minimal rainfall and location outside the hurricane belt\n", "    - A blend of Caribbean and Dutch culture\n", "    - Great dining options and nightlife\n", "    - Various water sports and activities\n", "    \n", "    Would you like me to get some specific hotel recommendations in Aruba for your stay? I can transfer you to our hotel advisor who can help with accommodations.\n", "    \n", "    --- Conversation Turn 2 ---\n", "    \n", "    User: Command(resume='could you recommend a nice hotel in one of the areas and tell me which area it is.')\n", "    \n", "    hotel_advisor: Based on the recommendations, I can suggest two excellent options:\n", "    \n", "    1. The Ritz-Carlton, Aruba - Located in Palm Beach\n", "    - This luxury resort is situated in the vibrant Palm Beach area\n", "    - Known for its exceptional service and amenities\n", "    - Perfect if you want to be close to dining, shopping, and entertainment\n", "    - Features multiple restaurants, a casino, and a world-class spa\n", "    - Located on a pristine stretch of Palm Beach\n", "    \n", "    2. Bucuti & Tara Beach Resort - Located in Eagle Beach\n", "    - An adults-only boutique resort on Eagle Beach\n", "    - Known for being more intimate and peaceful\n", "    - Award-winning for its sustainability practices\n", "    - Perfect for a romantic getaway or peaceful vacation\n", "    - Located on one of the most beautiful beaches in the Caribbean\n", "    \n", "    Would you like more specific information about either of these properties or their locations?\n", "    \n", "    --- Conversation Turn 3 ---\n", "    \n", "    User: Command(resume='i like the first one. could you recommend something to do near the hotel?')\n", "    \n", "    travel_advisor: Near the Ritz-Carlton in Palm Beach, here are some highly recommended activities:\n", "    \n", "    1. Visit the Palm Beach Plaza Mall - Just a short walk from the hotel, featuring shopping, dining, and entertainment\n", "    2. Try your luck at the Stellaris Casino - It's right in the Ritz-Carlton\n", "    3. Take a sunset sailing cruise - Many depart from the nearby pier\n", "    4. Visit the California Lighthouse - A scenic landmark just north of Palm Beach\n", "    5. Enjoy water sports at Palm Beach:\n", "       - Jet skiing\n", "       - Parasailing\n", "       - Snorkeling\n", "       - Stand-up paddleboarding\n", "    \n", "    Would you like more specific information about any of these activities or would you like to know about other options in the area?\n", "    ```"]}, {"cell_type": "markdown", "id": "04d18c63-a0eb-45ac-86dc-0cc5bd683973", "metadata": {}, "source": ["## Prebuilt implementations"]}, {"cell_type": "markdown", "id": "e0e4ce57-f8de-4f37-836e-c1e1a02dd7b7", "metadata": {}, "source": ["LangGraph comes with prebuilt implementations of two of the most popular multi-agent architectures:\n", "\n", "- [supervisor](../../agents/multi-agent#supervisor) — individual agents are coordinated by a central supervisor agent. The supervisor controls all communication flow and task delegation, making decisions about which agent to invoke based on the current context and task requirements. You can use [`langgraph-supervisor`](https://github.com/langchain-ai/langgraph-supervisor-py) library to create a supervisor multi-agent systems.\n", "- [swarm](../../agents/multi-agent#supervisor) — agents dynamically hand off control to one another based on their specializations. The system remembers which agent was last active, ensuring that on subsequent interactions, the conversation resumes with that agent. You can use [`langgraph-swarm`](https://github.com/langchain-ai/langgraph-swarm-py) library to create a swarm multi-agent systems."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}