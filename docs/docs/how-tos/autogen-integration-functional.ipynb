{"cells": [{"cell_type": "markdown", "id": "100c0c81-6a9f-4ba1-b1a8-42aae82b7172", "metadata": {}, "source": ["# How to integrate LangGraph (functional API) with AutoGen, CrewAI, and other frameworks\n", "\n", "LangGraph is a framework for building agentic and multi-agent applications. LangGraph can be easily integrated with other agent frameworks. \n", "\n", "The primary reasons you might want to integrate LangGraph with other agent frameworks:\n", "\n", "- create [multi-agent systems](../../concepts/multi_agent) where individual agents are built with different frameworks\n", "- leverage LangGraph to add features like [persistence](../../concepts/persistence), [streaming](../../concepts/streaming), [short and long-term memory](../../concepts/memory) and more\n", "\n", "The simplest way to integrate agents from other frameworks is by calling those agents inside a LangGraph [node](../../concepts/low_level/#nodes):\n", "\n", "```python\n", "import autogen\n", "from langgraph.func import entrypoint, task\n", "\n", "autogen_agent = autogen.AssistantAgent(name=\"assistant\", ...)\n", "user_proxy = autogen.UserProxyAgent(name=\"user_proxy\", ...)\n", "\n", "@task\n", "def call_autogen_agent(messages):\n", "    response = user_proxy.initiate_chat(\n", "        autogen_agent,\n", "        message=messages[-1],\n", "        ...\n", "    )\n", "    ...\n", "\n", "\n", "@entrypoint()\n", "def workflow(messages):\n", "    response = call_autogen_agent(messages).result()\n", "    return response\n", "\n", "\n", "workflow.invoke(\n", "    [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"Find numbers between 10 and 30 in fi<PERSON><PERSON><PERSON> sequence\",\n", "        }\n", "    ]\n", ")\n", "```\n", "\n", "In this guide we show how to build a LangGraph chatbot that integrates with AutoGen, but you can follow the same approach with other frameworks."]}, {"cell_type": "markdown", "id": "b189ceb2-132b-4c7b-81b4-c7b8b062f833", "metadata": {}, "source": ["## Setup"]}, {"cell_type": "code", "execution_count": 1, "id": "62417d3a-94f9-4a52-9962-12639d714966", "metadata": {}, "outputs": [], "source": ["%pip install autogen langgraph"]}, {"cell_type": "code", "execution_count": 2, "id": "d46da41d-0a71-4654-aec8-9e6ad8765236", "metadata": {}, "outputs": [{"name": "stdin", "output_type": "stream", "text": ["OPENAI_API_KEY:  ········\n"]}], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "\n", "_set_env(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "id": "1926bbc3-6b06-41e0-9604-860a2bbf8fa3", "metadata": {}, "source": ["## Define AutoGen agent\n", "\n", "Here we define our AutoGen agent. Adapted from official tutorial [here](https://github.com/microsoft/autogen/blob/0.2/notebook/agentchat_web_info.ipynb)."]}, {"cell_type": "code", "execution_count": 3, "id": "524de117-ff09-4b26-bfe8-a9f85a46ffd5", "metadata": {}, "outputs": [], "source": ["import autogen\n", "import os\n", "\n", "config_list = [{\"model\": \"gpt-4o\", \"api_key\": os.environ[\"OPENAI_API_KEY\"]}]\n", "\n", "llm_config = {\n", "    \"timeout\": 600,\n", "    \"cache_seed\": 42,\n", "    \"config_list\": config_list,\n", "    \"temperature\": 0,\n", "}\n", "\n", "autogen_agent = autogen.AssistantAgent(\n", "    name=\"assistant\",\n", "    llm_config=llm_config,\n", ")\n", "\n", "user_proxy = autogen.UserProxyAgent(\n", "    name=\"user_proxy\",\n", "    human_input_mode=\"NEVER\",\n", "    max_consecutive_auto_reply=10,\n", "    is_termination_msg=lambda x: x.get(\"content\", \"\").rstrip().endswith(\"TERMINATE\"),\n", "    code_execution_config={\n", "        \"work_dir\": \"web\",\n", "        \"use_docker\": <PERSON><PERSON><PERSON>,\n", "    },  # Please set use_docker=True if docker is available to run the generated code. Using docker is safer than running the generated code directly.\n", "    llm_config=llm_config,\n", "    system_message=\"Reply TERMINATE if the task has been solved at full satisfaction. Otherwise, reply CONTINUE, or the reason why the task is not solved yet.\",\n", ")"]}, {"cell_type": "markdown", "id": "8aa858e2-4acb-4f75-be20-b9ccbbcb5073", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "dcc478f5-4a35-43f8-bf59-9cb71289cd00", "metadata": {}, "source": ["## Create the workflow\n", "\n", "We will now create a LangGraph chatbot graph that calls AutoGen agent."]}, {"cell_type": "code", "execution_count": 8, "id": "d129e4e1-3766-429a-b806-cde3d8bc0469", "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import convert_to_openai_messages, BaseMessage\n", "from langgraph.func import entrypoint, task\n", "from langgraph.graph import add_messages\n", "from langgraph.checkpoint.memory import MemorySaver\n", "\n", "\n", "@task\n", "def call_autogen_agent(messages: list[BaseMessage]):\n", "    # convert to openai-style messages\n", "    messages = convert_to_openai_messages(messages)\n", "    response = user_proxy.initiate_chat(\n", "        autogen_agent,\n", "        message=messages[-1],\n", "        # pass previous message history as context\n", "        carryover=messages[:-1],\n", "    )\n", "    # get the final response from the agent\n", "    content = response.chat_history[-1][\"content\"]\n", "    return {\"role\": \"assistant\", \"content\": content}\n", "\n", "\n", "# add short-term memory for storing conversation history\n", "checkpointer = MemorySaver()\n", "\n", "\n", "@entrypoint(checkpointer=checkpointer)\n", "def workflow(messages: list[BaseMessage], previous: list[BaseMessage]):\n", "    messages = add_messages(previous or [], messages)\n", "    response = call_autogen_agent(messages).result()\n", "    return entrypoint.final(value=response, save=add_messages(messages, response))"]}, {"cell_type": "markdown", "id": "23d629c3-1d6b-40af-adf6-915e15657566", "metadata": {}, "source": ["## Run the graph\n", "\n", "We can now run the graph."]}, {"cell_type": "code", "execution_count": 10, "id": "a279b667-0f5d-4008-8d43-c806a3f379c4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33muser_proxy\u001b[0m (to assistant):\n", "\n", "Find numbers between 10 and 30 in <PERSON><PERSON><PERSON><PERSON> sequence\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33massistant\u001b[0m (to user_proxy):\n", "\n", "To find numbers between 10 and 30 in the <PERSON><PERSON><PERSON><PERSON> sequence, we can generate the <PERSON><PERSON><PERSON><PERSON> sequence and check which numbers fall within this range. Here's a plan:\n", "\n", "1. Generate <PERSON> numbers starting from 0.\n", "2. Continue generating until the numbers exceed 30.\n", "3. Collect and print the numbers that are between 10 and 30.\n", "\n", "Let's implement this in Python:\n", "\n", "```python\n", "# filename: fibonacci_range.py\n", "\n", "def fibonacci_sequence():\n", "    a, b = 0, 1\n", "    while a <= 30:\n", "        if 10 <= a <= 30:\n", "            print(a)\n", "        a, b = b, a + b\n", "\n", "fibonacci_sequence()\n", "```\n", "\n", "This script will print the <PERSON><PERSON><PERSON><PERSON> numbers between 10 and 30. Please execute the code to see the result.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> EXECUTING CODE BLOCK 0 (inferred language is python)...\u001b[0m\n", "\u001b[33muser_proxy\u001b[0m (to assistant):\n", "\n", "exitcode: 0 (execution succeeded)\n", "Code output: \n", "13\n", "21\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33massistant\u001b[0m (to user_proxy):\n", "\n", "The Fibonacci numbers between 10 and 30 are 13 and 21. \n", "\n", "These numbers are part of the <PERSON><PERSON><PERSON><PERSON> sequence, which is generated by adding the two preceding numbers to get the next number, starting from 0 and 1. \n", "\n", "The sequence goes: 0, 1, 1, 2, 3, 5, 8, 13, 21, 34, ...\n", "\n", "As you can see, 13 and 21 are the only numbers in this sequence that fall between 10 and 30.\n", "\n", "TERMINATE\n", "\n", "--------------------------------------------------------------------------------\n", "{'call_autogen_agent': {'role': 'assistant', 'content': 'The Fibonacci numbers between 10 and 30 are 13 and 21. \\n\\nThese numbers are part of the <PERSON><PERSON><PERSON><PERSON> sequence, which is generated by adding the two preceding numbers to get the next number, starting from 0 and 1. \\n\\nThe sequence goes: 0, 1, 1, 2, 3, 5, 8, 13, 21, 34, ...\\n\\nAs you can see, 13 and 21 are the only numbers in this sequence that fall between 10 and 30.\\n\\nTERMINATE'}}\n", "{'workflow': {'role': 'assistant', 'content': 'The Fibonacci numbers between 10 and 30 are 13 and 21. \\n\\nThese numbers are part of the <PERSON><PERSON><PERSON><PERSON> sequence, which is generated by adding the two preceding numbers to get the next number, starting from 0 and 1. \\n\\nThe sequence goes: 0, 1, 1, 2, 3, 5, 8, 13, 21, 34, ...\\n\\nAs you can see, 13 and 21 are the only numbers in this sequence that fall between 10 and 30.\\n\\nTERMINATE'}}\n"]}], "source": ["# pass the thread ID to persist agent outputs for future interactions\n", "# highlight-next-line\n", "config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "\n", "for chunk in workflow.stream(\n", "    [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"Find numbers between 10 and 30 in fi<PERSON><PERSON><PERSON> sequence\",\n", "        }\n", "    ],\n", "    # highlight-next-line\n", "    config,\n", "):\n", "    print(chunk)"]}, {"cell_type": "markdown", "id": "c6cd57b4-d4ee-49f6-be12-318613849669", "metadata": {}, "source": ["Since we're leveraging LangGraph's [persistence](https://langchain-ai.github.io/langgraph/concepts/persistence/) features we can now continue the conversation using the same thread ID -- LangGraph will automatically pass previous history to the AutoGen agent:"]}, {"cell_type": "code", "execution_count": 12, "id": "e68811a7-962e-4fe3-9f45-9b99ebbe04e7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33muser_proxy\u001b[0m (to assistant):\n", "\n", "Multiply the last number by 3\n", "Context: \n", "Find numbers between 10 and 30 in <PERSON><PERSON><PERSON><PERSON> sequence\n", "The Fibonacci numbers between 10 and 30 are 13 and 21. \n", "\n", "These numbers are part of the <PERSON><PERSON><PERSON><PERSON> sequence, which is generated by adding the two preceding numbers to get the next number, starting from 0 and 1. \n", "\n", "The sequence goes: 0, 1, 1, 2, 3, 5, 8, 13, 21, 34, ...\n", "\n", "As you can see, 13 and 21 are the only numbers in this sequence that fall between 10 and 30.\n", "\n", "TERMINATE\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33massistant\u001b[0m (to user_proxy):\n", "\n", "The last number in the <PERSON><PERSON><PERSON><PERSON> sequence between 10 and 30 is 21. Multiplying 21 by 3 gives:\n", "\n", "21 * 3 = 63\n", "\n", "TERMINATE\n", "\n", "--------------------------------------------------------------------------------\n", "{'call_autogen_agent': {'role': 'assistant', 'content': 'The last number in the <PERSON><PERSON><PERSON><PERSON> sequence between 10 and 30 is 21. Multiplying 21 by 3 gives:\\n\\n21 * 3 = 63\\n\\nTERMINATE'}}\n", "{'workflow': {'role': 'assistant', 'content': 'The last number in the <PERSON><PERSON><PERSON><PERSON> sequence between 10 and 30 is 21. Multiplying 21 by 3 gives:\\n\\n21 * 3 = 63\\n\\nTERMINATE'}}\n"]}], "source": ["for chunk in workflow.stream(\n", "    [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"Multiply the last number by 3\",\n", "        }\n", "    ],\n", "    # highlight-next-line\n", "    config,\n", "):\n", "    print(chunk)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}