{"cells": [{"attachments": {"18f6888d-c412-4c53-ac3c-239fb90d2b6c.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "d72fae4e-f7de-42b7-91ee-bdd0a57ae46c", "metadata": {}, "source": ["# Prompt Generation from User Requirements\n", "\n", "In this example we will create a chat bot that helps a user generate a prompt.\n", "It will first collect requirements from the user, and then will generate the prompt (and refine it based on user input).\n", "These are split into two separate states, and the LLM decides when to transition between them.\n", "\n", "A graphical representation of the system can be found below.\n", "\n", "![prompt-generator.png](attachment:18f6888d-c412-4c53-ac3c-239fb90d2b6c.png)"]}, {"cell_type": "markdown", "id": "bb66b808", "metadata": {}, "source": ["## Setup\n", "\n", "First, let's install our required packages and set our OpenAI API key (the LLM we will use)"]}, {"cell_type": "code", "execution_count": null, "id": "9aa583d7", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "% pip install -U langgraph langchain_openai"]}, {"cell_type": "code", "execution_count": null, "id": "7cd84ff0", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "\n", "_set_env(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "id": "105a371d", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>"]}, {"cell_type": "markdown", "id": "6d78b593-ba26-4c90-b2e2-83119e47679f", "metadata": {}, "source": ["## Gather information\n", "\n", "First, let's define the part of the graph that will gather user requirements. This will be an LLM call with a specific system message. It will have access to a tool that it can call when it is ready to generate the prompt."]}, {"cell_type": "markdown", "id": "0d731dcc-8295-498d-a95f-644ce24a717e", "metadata": {}, "source": ["<div class=\"admonition note\">\n", "    <p class=\"admonition-title\">Using Pydantic with <PERSON><PERSON><PERSON><PERSON></p>\n", "    <p>\n", "        This notebook uses Pydantic v2 <code>BaseModel</code>, which requires <code>langchain-core >= 0.3</code>. Using <code>langchain-core < 0.3</code> will result in errors due to mixing of Pydantic v1 and v2 <code>BaseModels</code>.\n", "    </p>\n", "</div>"]}, {"cell_type": "code", "execution_count": 1, "id": "53216ab5-2cd3-48a4-8778-41ba10f72519", "metadata": {}, "outputs": [], "source": ["from typing import List\n", "\n", "from langchain_core.messages import SystemMessage\n", "from langchain_openai import ChatOpenAI\n", "\n", "from pydantic import BaseModel"]}, {"cell_type": "code", "execution_count": 2, "id": "5f795b78-004d-40ca-95d6-069f67e4f9c9", "metadata": {}, "outputs": [], "source": ["template = \"\"\"Your job is to get information from a user about what type of prompt template they want to create.\n", "\n", "You should get the following information from them:\n", "\n", "- What the objective of the prompt is\n", "- What variables will be passed into the prompt template\n", "- Any constraints for what the output should NOT do\n", "- Any requirements that the output MUST adhere to\n", "\n", "If you are not able to discern this info, ask them to clarify! Do not attempt to wildly guess.\n", "\n", "After you are able to discern all the information, call the relevant tool.\"\"\"\n", "\n", "\n", "def get_messages_info(messages):\n", "    return [SystemMessage(content=template)] + messages\n", "\n", "\n", "class PromptInstructions(BaseModel):\n", "    \"\"\"Instructions on how to prompt the LLM.\"\"\"\n", "\n", "    objective: str\n", "    variables: List[str]\n", "    constraints: List[str]\n", "    requirements: List[str]\n", "\n", "\n", "llm = ChatOpenAI(temperature=0)\n", "llm_with_tool = llm.bind_tools([PromptInstructions])\n", "\n", "\n", "def info_chain(state):\n", "    messages = get_messages_info(state[\"messages\"])\n", "    response = llm_with_tool.invoke(messages)\n", "    return {\"messages\": [response]}"]}, {"cell_type": "markdown", "id": "bb40630f-83c7-4283-a6dd-04231805a7ed", "metadata": {}, "source": ["## Generate Prompt\n", "\n", "We now set up the state that will generate the prompt.\n", "This will require a separate system message, as well as a function to filter out all message PRIOR to the tool invocation (as that is when the previous state decided it was time to generate the prompt"]}, {"cell_type": "code", "execution_count": 3, "id": "ca9a0234-bbeb-4bff-8276-8dde499c3390", "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import AIMessage, HumanMessage, ToolMessage\n", "\n", "# New system prompt\n", "prompt_system = \"\"\"Based on the following requirements, write a good prompt template:\n", "\n", "{reqs}\"\"\"\n", "\n", "\n", "# Function to get the messages for the prompt\n", "# Will only get messages AFTER the tool call\n", "def get_prompt_messages(messages: list):\n", "    tool_call = None\n", "    other_msgs = []\n", "    for m in messages:\n", "        if isinstance(m, AIMessage) and m.tool_calls:\n", "            tool_call = m.tool_calls[0][\"args\"]\n", "        elif isinstance(m, ToolMessage):\n", "            continue\n", "        elif tool_call is not None:\n", "            other_msgs.append(m)\n", "    return [SystemMessage(content=prompt_system.format(reqs=tool_call))] + other_msgs\n", "\n", "\n", "def prompt_gen_chain(state):\n", "    messages = get_prompt_messages(state[\"messages\"])\n", "    response = llm.invoke(messages)\n", "    return {\"messages\": [response]}"]}, {"cell_type": "markdown", "id": "8dbabda8-34f0-4eef-bce2-ad3ff505366b", "metadata": {}, "source": ["## Define the state logic\n", "\n", "This is the logic for what state the chatbot is in.\n", "If the last message is a tool call, then we are in the state where the \"prompt creator\" (`prompt`) should respond.\n", "Otherwise, if the last message is not a HumanMessage, then we know the human should respond next and so we are in the `END` state.\n", "If the last message is a HumanMessage, then if there was a tool call previously we are in the `prompt` state.\n", "Otherwise, we are in the \"info gathering\" (`info`) state."]}, {"cell_type": "code", "execution_count": 4, "id": "74f29e15-20e2-420c-a450-84e929f16e4e", "metadata": {}, "outputs": [], "source": ["from typing import Literal\n", "\n", "from langgraph.graph import END\n", "\n", "\n", "def get_state(state):\n", "    messages = state[\"messages\"]\n", "    if isinstance(messages[-1], AIMessage) and messages[-1].tool_calls:\n", "        return \"add_tool_message\"\n", "    elif not isinstance(messages[-1], HumanMessage):\n", "        return END\n", "    return \"info\""]}, {"cell_type": "markdown", "id": "b76bea78-07a5-418f-9b7c-71c376d4b6f7", "metadata": {}, "source": ["## Create the graph\n", "\n", "We can now the create the graph.\n", "We will use a SqliteSaver to persist conversation history."]}, {"cell_type": "code", "execution_count": 5, "id": "59d9d6b4-dce4-43cc-9a1a-61a7912ed5b8", "metadata": {}, "outputs": [], "source": ["from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.graph import StateGraph, START\n", "from langgraph.graph.message import add_messages\n", "from typing import Annotated\n", "from typing_extensions import TypedDict\n", "\n", "\n", "class State(TypedDict):\n", "    messages: Annotated[list, add_messages]\n", "\n", "\n", "memory = MemorySaver()\n", "workflow = StateGraph(State)\n", "workflow.add_node(\"info\", info_chain)\n", "workflow.add_node(\"prompt\", prompt_gen_chain)\n", "\n", "\n", "@workflow.add_node\n", "def add_tool_message(state: State):\n", "    return {\n", "        \"messages\": [\n", "            ToolMessage(\n", "                content=\"Prompt generated!\",\n", "                tool_call_id=state[\"messages\"][-1].tool_calls[0][\"id\"],\n", "            )\n", "        ]\n", "    }\n", "\n", "\n", "workflow.add_conditional_edges(\"info\", get_state, [\"add_tool_message\", \"info\", END])\n", "workflow.add_edge(\"add_tool_message\", \"prompt\")\n", "workflow.add_edge(\"prompt\", END)\n", "workflow.add_edge(START, \"info\")\n", "graph = workflow.compile(checkpointer=memory)"]}, {"cell_type": "code", "execution_count": 6, "id": "1b1613e0", "metadata": {}, "outputs": [{"data": {"image/jpeg": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "afcf523c-265d-45cf-a981-fc50c50c1738", "metadata": {}, "source": ["## Use the graph\n", "\n", "We can now use the created chatbot."]}, {"cell_type": "code", "execution_count": 10, "id": "25793988-45a2-4e65-b33c-64e72aadb10e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["User (q/Q to quit): hi!\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Hello! How can I assist you today?\n", "User (q/Q to quit): rag prompt\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Sure! I can help you create a prompt template. To get started, could you please provide me with the following information:\n", "\n", "1. What is the objective of the prompt?\n", "2. What variables will be passed into the prompt template?\n", "3. Any constraints for what the output should NOT do?\n", "4. Any requirements that the output MUST adhere to?\n", "\n", "Once I have this information, I can assist you in creating the prompt template.\n", "User (q/Q to quit): 1 rag, 2 none, 3 no, 4 no\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  PromptInstructions (call_tcz0foifsaGKPdZmsZxNnepl)\n", " Call ID: call_tcz0foifsaGKPdZmsZxNnepl\n", "  Args:\n", "    objective: rag\n", "    variables: ['none']\n", "    constraints: ['no']\n", "    requirements: ['no']\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "\n", "Prompt generated!\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Please write a response using the RAG (Red, Amber, Green) rating system.\n", "Done!\n", "User (q/Q to quit): red\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Response: The status is RED.\n", "User (q/Q to quit): q\n", "AI: <PERSON><PERSON><PERSON>\n"]}], "source": ["import uuid\n", "\n", "cached_human_responses = [\"hi!\", \"rag prompt\", \"1 rag, 2 none, 3 no, 4 no\", \"red\", \"q\"]\n", "cached_response_index = 0\n", "config = {\"configurable\": {\"thread_id\": str(uuid.uuid4())}}\n", "while True:\n", "    try:\n", "        user = input(\"User (q/Q to quit): \")\n", "    except:\n", "        user = cached_human_responses[cached_response_index]\n", "        cached_response_index += 1\n", "    print(f\"User (q/Q to quit): {user}\")\n", "    if user in {\"q\", \"Q\"}:\n", "        print(\"AI: <PERSON><PERSON><PERSON>\")\n", "        break\n", "    output = None\n", "    for output in graph.stream(\n", "        {\"messages\": [HumanMessage(content=user)]}, config=config, stream_mode=\"updates\"\n", "    ):\n", "        last_message = next(iter(output.values()))[\"messages\"][-1]\n", "        last_message.pretty_print()\n", "\n", "    if output and \"prompt\" in output:\n", "        print(\"Done!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}