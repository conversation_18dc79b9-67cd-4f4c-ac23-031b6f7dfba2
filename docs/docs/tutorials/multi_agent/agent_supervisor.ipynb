{"cells": [{"attachments": {"8ee0a8ce-f0a8-4019-b5bf-b20933e40956.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "a3e3ebc4-57af-4fe4-bdd3-36aff67bf276", "metadata": {}, "source": ["# Multi-agent supervisor\n", "\n", "[**Supervisor**](../../../concepts/multi_agent#supervisor) is a multi-agent architecture where **specialized** agents are coordinated by a central **supervisor agent**. The supervisor agent controls all communication flow and task delegation, making decisions about which agent to invoke based on the current context and task requirements.\n", "\n", "In this tutorial, you will build a supervisor system with two agents — a research and a math expert. By the end of the tutorial you will:\n", "\n", "1. Build specialized research and math agents\n", "2. Build a supervisor for orchestrating them with the prebuilt [`langgraph-supervisor`](https://langchain-ai.github.io/langgraph/agents/multi-agent/#supervisor)\n", "3. Build a supervisor from scratch\n", "4. Implement advanced task delegation\n", "\n", "![diagram](attachment:8ee0a8ce-f0a8-4019-b5bf-b20933e40956.png)\n", "\n", "## Setup\n", "\n", "First, let's install required packages and set our API keys"]}, {"cell_type": "code", "execution_count": 1, "id": "0d30b6f7-3bec-4d9f-af50-43dfdc81ae6c", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U langgraph langgraph-supervisor langchain-tavily \"langchain[openai]\""]}, {"cell_type": "code", "execution_count": 2, "id": "c84adef6-51b3-46c5-8490-c2a1ad967769", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_if_undefined(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"Please provide your {var}\")\n", "\n", "\n", "_set_if_undefined(\"OPENAI_API_KEY\")\n", "_set_if_undefined(\"TAVILY_API_KEY\")"]}, {"cell_type": "markdown", "id": "be85e3ad", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>"]}, {"cell_type": "markdown", "id": "349a5673-962e-4de9-ac19-70ee426ef433", "metadata": {}, "source": ["## 1. Create worker agents"]}, {"attachments": {}, "cell_type": "markdown", "id": "ad822cf5-56d4-47f7-ad64-d9fd3f57551e", "metadata": {}, "source": ["First, let's create our specialized worker agents — research agent and math agent:\n", "\n", "* Research agent will have access to a web search tool using [Tavily API](https://tavily.com/)\n", "* Math agent will have access to simple math tools (`add`, `multiply`, `divide`)"]}, {"attachments": {}, "cell_type": "markdown", "id": "5e34989a-8989-432e-b6aa-8de91ab83372", "metadata": {}, "source": ["### Research agent\n", "\n", "For web search, we will use `TavilySearch` tool from `langchain-tavily`:"]}, {"cell_type": "code", "execution_count": 3, "id": "0fdc7345-2967-484b-881a-114dfac3f534", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Find events, attractions, deals, and more at nyctourism.com Skip Main Navigation Menu The Official Website of the City of New York Text Size Powered by Translate SearchSearch Primary Navigation The official website of NYC Home NYC Resources NYC311 Office of the Mayor Events Connect Jobs Search Office of the Mayor | Mayor's Bio | City of New York Secondary Navigation MayorBiographyNewsOfficials <PERSON> 110th Mayor of New York City Mayor <PERSON> has served the people of New York City as an NYPD officer, State Senator, Brooklyn Borough President, and now as the 110th Mayor of the City of New York. Mayor <PERSON> has served the people of New York City as an NYPD officer, State Senator, Brooklyn Borough President, and now as the 110th Mayor of the City of New York. He gave voice to a diverse coalition of working families in all five boroughs and is leading the fight to bring back New York City’s economy, reduce inequality, improve public safety, and build a stronger, healthier city that delivers for all New Yorkers. As the representative of one of the nation’s largest counties, <PERSON> fought tirelessly to grow the local economy, invest in schools, reduce inequality, improve public safety, and advocate for smart policies and better government that delivers for all New Yorkers.\n"]}], "source": ["from langchain_tavily import TavilySearch\n", "\n", "web_search = TavilySearch(max_results=3)\n", "web_search_results = web_search.invoke(\"who is the mayor of NYC?\")\n", "\n", "print(web_search_results[\"results\"][0][\"content\"])"]}, {"cell_type": "markdown", "id": "cc899eb6-b08e-41b8-b92b-75908c44e8b0", "metadata": {}, "source": ["To create individual worker agents, we will use LangGraph's prebuilt [agent](../../../agents/agents#basic-configuration)."]}, {"cell_type": "code", "execution_count": 4, "id": "15e179e4-1a49-4c76-9235-0ca989eb0d2d", "metadata": {}, "outputs": [], "source": ["from langgraph.prebuilt import create_react_agent\n", "\n", "research_agent = create_react_agent(\n", "    model=\"openai:gpt-4.1\",\n", "    tools=[web_search],\n", "    prompt=(\n", "        \"You are a research agent.\\n\\n\"\n", "        \"INSTRUCTIONS:\\n\"\n", "        \"- Assist ONLY with research-related tasks, DO NOT do any math\\n\"\n", "        \"- After you're done with your tasks, respond to the supervisor directly\\n\"\n", "        \"- Respond ONLY with the results of your work, do NOT include ANY other text.\"\n", "    ),\n", "    name=\"research_agent\",\n", ")"]}, {"cell_type": "markdown", "id": "023ae2f2-517b-4611-a0ca-506b8c0b734e", "metadata": {}, "source": ["Let's [run the agent](../../../agents/run_agents) to verify that it behaves as expected. \n", "\n", "\n", "??? note \"We'll use `pretty_print_messages` helper to render the streamed agent outputs nicely\"\n", "\n", "    ```python\n", "    from langchain_core.messages import convert_to_messages\n", "    \n", "    \n", "    def pretty_print_message(message, indent=False):\n", "        pretty_message = message.pretty_repr(html=True)\n", "        if not indent:\n", "            print(pretty_message)\n", "            return\n", "    \n", "        indented = \"\\n\".join(\"\\t\" + c for c in pretty_message.split(\"\\n\"))\n", "        print(indented)\n", "    \n", "    \n", "    def pretty_print_messages(update, last_message=False):\n", "        is_subgraph = False\n", "        if isinstance(update, tuple):\n", "            ns, update = update\n", "            # skip parent graph updates in the printouts\n", "            if len(ns) == 0:\n", "                return\n", "    \n", "            graph_id = ns[-1].split(\":\")[0]\n", "            print(f\"Update from subgraph {graph_id}:\")\n", "            print(\"\\n\")\n", "            is_subgraph = True\n", "    \n", "        for node_name, node_update in update.items():\n", "            update_label = f\"Update from node {node_name}:\"\n", "            if is_subgraph:\n", "                update_label = \"\\t\" + update_label\n", "    \n", "            print(update_label)\n", "            print(\"\\n\")\n", "    \n", "            messages = convert_to_messages(node_update[\"messages\"])\n", "            if last_message:\n", "                messages = messages[-1:]\n", "    \n", "            for m in messages:\n", "                pretty_print_message(m, indent=is_subgraph)\n", "            print(\"\\n\")\n", "    ```"]}, {"cell_type": "code", "execution_count": 5, "id": "bfaa81e6", "metadata": {}, "outputs": [], "source": ["# hide-cell\n", "from langchain_core.messages import convert_to_messages\n", "\n", "\n", "def pretty_print_message(message, indent=False):\n", "    pretty_message = message.pretty_repr(html=True)\n", "    if not indent:\n", "        print(pretty_message)\n", "        return\n", "\n", "    indented = \"\\n\".join(\"\\t\" + c for c in pretty_message.split(\"\\n\"))\n", "    print(indented)\n", "\n", "\n", "def pretty_print_messages(update, last_message=False):\n", "    is_subgraph = False\n", "    if isinstance(update, tuple):\n", "        ns, update = update\n", "        # skip parent graph updates in the printouts\n", "        if len(ns) == 0:\n", "            return\n", "\n", "        graph_id = ns[-1].split(\":\")[0]\n", "        print(f\"Update from subgraph {graph_id}:\")\n", "        print(\"\\n\")\n", "        is_subgraph = True\n", "\n", "    for node_name, node_update in update.items():\n", "        update_label = f\"Update from node {node_name}:\"\n", "        if is_subgraph:\n", "            update_label = \"\\t\" + update_label\n", "\n", "        print(update_label)\n", "        print(\"\\n\")\n", "\n", "        messages = convert_to_messages(node_update[\"messages\"])\n", "        if last_message:\n", "            messages = messages[-1:]\n", "\n", "        for m in messages:\n", "            pretty_print_message(m, indent=is_subgraph)\n", "        print(\"\\n\")"]}, {"cell_type": "code", "execution_count": 6, "id": "71474c5b-6a9b-460e-bf30-77831976aaf9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Update from node agent:\n", "\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: research_agent\n", "Tool Calls:\n", "  tavily_search (call_U748rQhQXT36sjhbkYLSXQtJ)\n", " Call ID: call_U748rQhQXT36sjhbkYLSXQtJ\n", "  Args:\n", "    query: current mayor of New York City\n", "    search_depth: basic\n", "\n", "\n", "Update from node tools:\n", "\n", "\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: tavily_search\n", "\n", "{\"query\": \"current mayor of New York City\", \"follow_up_questions\": null, \"answer\": null, \"images\": [], \"results\": [{\"title\": \"List of mayors of New York City - Wikipedia\", \"url\": \"https://en.wikipedia.org/wiki/List_of_mayors_of_New_York_City\", \"content\": \"The mayor of New York City is the chief executive of the Government of New York City, as stipulated by New York City's charter.The current officeholder, the 110th in the sequence of regular mayors, is <PERSON>, a member of the Democratic Party.. During the Dutch colonial period from 1624 to 1664, New Amsterdam was governed by the Director of New Netherland.\", \"score\": 0.9039154, \"raw_content\": null}, {\"title\": \"Office of the Mayor | Mayor's Bio | City of New York - NYC.gov\", \"url\": \"https://www.nyc.gov/office-of-the-mayor/bio.page\", \"content\": \"Mayor <PERSON> has served the people of New York City as an NYPD officer, State Senator, Brooklyn Borough President, and now as the 110th Mayor of the City of New York. He gave voice to a diverse coalition of working families in all five boroughs and is leading the fight to bring back New York City's economy, reduce inequality, improve\", \"score\": 0.8405867, \"raw_content\": null}, {\"title\": \"<PERSON>\", \"url\": \"https://en.wikipedia.org/wiki/<PERSON>_<PERSON>\", \"content\": \"<PERSON> <PERSON> (born September 1, 1960) is an American politician and former police officer who has served as the 110th mayor of New York City since 2022. Adams was an officer in the New York City Transit Police and then the New York City Police Department (NYPD) for more than 20 years, retiring at the rank of captain.He served in the New York State Senate from 2006 to 2013, representing the\", \"score\": 0.77731717, \"raw_content\": null}], \"response_time\": 1.81}\n", "\n", "\n", "Update from node agent:\n", "\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: research_agent\n", "\n", "The current mayor of New York City is <PERSON>.\n", "\n", "\n"]}], "source": ["for chunk in research_agent.stream(\n", "    {\"messages\": [{\"role\": \"user\", \"content\": \"who is the mayor of NYC?\"}]}\n", "):\n", "    pretty_print_messages(chunk)"]}, {"attachments": {}, "cell_type": "markdown", "id": "95e76c28-e9c9-42b7-8381-5e2a07f900cb", "metadata": {}, "source": ["### Math agent\n", "\n", "For math agent tools we will use [vanilla Python functions](../../../agents/tools#define-simple-tools):"]}, {"cell_type": "code", "execution_count": 7, "id": "95910bb8-7144-4015-9ea4-d97d0c22db12", "metadata": {}, "outputs": [], "source": ["def add(a: float, b: float):\n", "    \"\"\"Add two numbers.\"\"\"\n", "    return a + b\n", "\n", "\n", "def multiply(a: float, b: float):\n", "    \"\"\"Multiply two numbers.\"\"\"\n", "    return a * b\n", "\n", "\n", "def divide(a: float, b: float):\n", "    \"\"\"Divide two numbers.\"\"\"\n", "    return a / b\n", "\n", "\n", "math_agent = create_react_agent(\n", "    model=\"openai:gpt-4.1\",\n", "    tools=[add, multiply, divide],\n", "    prompt=(\n", "        \"You are a math agent.\\n\\n\"\n", "        \"INSTRUCTIONS:\\n\"\n", "        \"- Assist ONLY with math-related tasks\\n\"\n", "        \"- After you're done with your tasks, respond to the supervisor directly\\n\"\n", "        \"- Respond ONLY with the results of your work, do NOT include ANY other text.\"\n", "    ),\n", "    name=\"math_agent\",\n", ")"]}, {"cell_type": "markdown", "id": "0df97bc0-f38b-4893-a135-c16cecbfa759", "metadata": {}, "source": ["Let's run the math agent:"]}, {"cell_type": "code", "execution_count": 8, "id": "f803943c-6dd2-4b42-93ba-0210ec276132", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Update from node agent:\n", "\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: math_agent\n", "Tool Calls:\n", "  add (call_p6OVLDHB4LyCNCxPOZzWR15v)\n", " Call ID: call_p6OVLDHB4LyCNCxPOZzWR15v\n", "  Args:\n", "    a: 3\n", "    b: 5\n", "\n", "\n", "Update from node tools:\n", "\n", "\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: add\n", "\n", "8.0\n", "\n", "\n", "Update from node agent:\n", "\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: math_agent\n", "Tool Calls:\n", "  multiply (call_EoaWHMLFZAX4AkajQCtZvbli)\n", " Call ID: call_EoaWHMLFZAX4AkajQCtZvbli\n", "  Args:\n", "    a: 8\n", "    b: 7\n", "\n", "\n", "Update from node tools:\n", "\n", "\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: multiply\n", "\n", "56.0\n", "\n", "\n", "Update from node agent:\n", "\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: math_agent\n", "\n", "56\n", "\n", "\n"]}], "source": ["for chunk in math_agent.stream(\n", "    {\"messages\": [{\"role\": \"user\", \"content\": \"what's (3 + 5) x 7\"}]}\n", "):\n", "    pretty_print_messages(chunk)"]}, {"cell_type": "markdown", "id": "dceefc4c-95e1-4565-8946-4c113a11416a", "metadata": {}, "source": ["## 2. Create supervisor with `langgraph-supervisor`"]}, {"cell_type": "markdown", "id": "eb5637a4-8ecb-4c2c-82f5-3674205e37b5", "metadata": {}, "source": ["To implement out multi-agent system, we will use [`create_supervisor`][langgraph_supervisor.supervisor.create_supervisor] from the prebuilt `langgraph-supervisor` library:"]}, {"cell_type": "code", "execution_count": 9, "id": "6f32a820-1f3d-4698-831b-46252e2316bc", "metadata": {}, "outputs": [], "source": ["from langgraph_supervisor import create_supervisor\n", "from langchain.chat_models import init_chat_model\n", "\n", "supervisor = create_supervisor(\n", "    model=init_chat_model(\"openai:gpt-4.1\"),\n", "    agents=[research_agent, math_agent],\n", "    prompt=(\n", "        \"You are a supervisor managing two agents:\\n\"\n", "        \"- a research agent. Assign research-related tasks to this agent\\n\"\n", "        \"- a math agent. Assign math-related tasks to this agent\\n\"\n", "        \"Assign work to one agent at a time, do not call agents in parallel.\\n\"\n", "        \"Do not do any work yourself.\"\n", "    ),\n", "    add_handoff_back_messages=True,\n", "    output_mode=\"full_history\",\n", ").compile()"]}, {"cell_type": "code", "execution_count": 10, "id": "4c45a3c2-4d9e-4760-87bc-3cc2c007a167", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAcoAAAD5CAIAAAAoZ+I8AAAQAElEQVR4nOzdB1gU1xYA4AsLu8DSm4CgooIKKBKxoYKKWKIo9t6NGivW2GLBEjV2sffG09gRFewNewEFRQQEFOmdpWzjHZhkQxRQkYHZ2fN/fuvszFZ25sy5587cUSksLCQIIYQqmwpBCCFEAwyvCCFECwyvCCFECwyvCCFECwyvCCFECwyvCCFECwyvSCFkJIuy00W5WZLcbLFIKB8HI3J5ymqayhpaKlp6KnrGXILkjRIe94pYLDEmP/JVzvuQXF1jVVG+VEObw9dRUVVVJvJAIi7MyRDD/oCrppwaL7S049dtzDe1VCdITmB4ReyUliC8fyFFjc+BvA8Ck76JfGd/6UnC9yGC9ERhTqbYyd3QqCaPIMbD8IpYCALr+1ABhCFLWz5hl9iwXPh25lbqbT2MCGI2DK+Ibf63NrZ5F7369lqEvWDnce9cypDfanFUlAhiKvkoQiH0LSSSQu8ZEW7DarA7tgLIyt3Hm+6aFwn1WYKYCrNXxBJSSeH2OZFTNtQnimTnb5FjlllC3xdBzIO/CmIJqAlAY5koGPjK8MUJYiTMXhEb3D2XDL09lraaRPF8CBdEBgva9zcmiGEwe0VyLyEmP+F9vmLGVmBhzU9PEn18l0sQw2B4RXLv/oUUJ3dDosCc3A3uX0gliGEwvCL5Fhueq2/CrVlfoc9lqlFLzcRS7X1oDkFMguEVybfIFzmGeAoTIcYWvIgXAoKYBMMrkm/vQwVVf2qWm5tbXFwc+U6RkZE9evQg9KhrpxkVgtkrs2B4RXIsMTbP1FKNr12lA7/Fx8enp6eT7/f69WtCG66asqUd/2MEdnAxCIZXJMcyk8XKtJ0VWlhY6OPjM2TIkDZt2gwbNszb21sikTx9+tTd3R2W9urVa9asWaQ4J12zZk2/fv2cnJzgYadOnaKeHhER4ejoeO/eva5duw4ePHjnzp3Lli1LSEiAmceOHSM0UFVVzkgWEcQYON4rkmOCLDF9qevx48f379/v6ekJ4fXWrVvbtm3j8/mjR4/etGkTzDx//nzNmjXhYevXr//06dPChQuVlJSio6Mh1JqamsJTVFVVYenevXuHDx/etGlTW1tboVB45coVPz8/Qg8NbU5uloQgxsDwiuSYIFPC1+UQejx//tzGxoaqlvbu3bt58+a5uaU0vf/44w+BQGBmZgbTkJn6+vrev38fwitEW5jTqlWroUOHkirB11VJji0giDEwvCJ5pkRUaBsb297efuvWrV5eXg4ODs7Ozubm5qU+DGoIkOcGBgbGxMRQc6isltKoUSNSVVRUlZTo2tegisDwiuSYOp+TnU5XtRGqrlANuH37NtRMVVRU3Nzcpk2bZmT0n1FWpVLp9OnTodU/ZcoUSF21tLTGjh1b8gE8XtUdNJaTLuapY3xlEAyvSI5BtTE9SUjooays3LtYVFTU48ePd+/enZOTs3HjxpKPCQsLCw0N3b59e4sWLag52dnZxsbVc/o/lEr0TFQJYgw8cgDJMW0DFQ5tGQL0QUVGRsJE3bp1Bw0aBL3/b9++/ewxGRkZcCuLp1HFSDVRUiY6BhheGQTDK5JjNetpvH2WIyqQEhr4+/vPmTPnzp07mZmZ9+7du3HjBlRjYX6dOnXg9urVqyEhIRB5oW5w5MiRrKys6OjoP//8E/qy4uPjS33BWrVqpaSk3Lp1S1alrVwv72bWbsS2i9/INQyvSL5Z2vLfh9JyMuiiRYsges6cOdPV1XX58uUuLi4LFy6E+dDH5e7uvnPnTuj4MjExWbFixatXrzp27DhjxozJkyf369cPwi7cfvmCbdu2bdq06ezZswMCAkhli3qVY2mHsZVZcLxXJN8igrITYwva9FToEbPAg4up+iaqDZppE8QYmL0i+Va/qVbRFapp6+CSC9npordPszG2Mg1mr0juQbv4zePs7mNNS10KJdFRo0aVukhJqcz138PDw9PTk9ADXjkoKKjURTo6OlDqLXURFIK7d+9e6qKAIwmWNnzrZiy/gKPcwfCK2ODqsQR7Z11jC7UvF0kkklLPtgJ5eXnq6qUPFKuqqqqmpkboAZ8HPlWpi0QiEXU27Zfg85S6KC1R+Ng/tevI0vcuqBpheEUssX1WxIS19TgcukZ4YSyF/eLMh7VXxBKD59byWa1w10z1WRPTf4YFxlZmwuwVsUdulviMd9zQebWUlBUi3PxvbWyP8aZaungqAUNh9orYQ0Nbpdtok22zI1M+sXzgqPSkgu2zI1wHG2NsZTLMXhELXTmSIJUWXT9VW59t0ScnQ3zfL0UqKXQbasJRwZoAo2F4Rez07kX2/QupDRy1TGqr1bFlw+lMMW8ECTH5rx9mOfUwhO9FEONheEVs9vZpNsTZ6Ne5jdvpKCsTvraKpo6KCk8+amJiYaEgUyTIlBBSGHw308Jaw8pBs1ELPHdAbmB4RQqgkESFCrJSRIIscW62RJhfyUPAxMfHS6XSkqNoVwqeurIan8PX4egYqtZpxFfGwwPkDYZXhH7Uvn37CgoKJk2aRBAqAYfTRgghWmB4RQghWmB4RQghWmB4RQghWmB4RQghWmB4RQghWmB4RQghWmB4RQghWmB4RQghWmB4RQghWmB4RQghWmB4RQghWmB4RQghWmB4RQghWmB4RQghWmB4RQghWmB4RQghWmB4RQghWmB4RQghWmB4RQghWmB4RQghWmB4RQghWmB4RQghWmB4RehH8Xg8JSUlgtB/YXhF6EcVFCMI/ReGV4QQogWGV4QQogWGV4QQogWGV4QQogWGV4QQogWGV4QQogWGV4QQogWGV4QQogWGV4QQogWGV4QQogWGV4QQogWGV4QQogWGV4QQogWGV4QQogWGV4QQooVSYWEhQQh9P3d3dyUlJYlEIhAI4K62tjZMw8TFixcJQpi9IlRhFhYWjx49kl2nICcnRyqVtmrViiBUTJkghCpkzJgxRkZGJefo6ekNHTqUIFQMwytCFeTo6NioUaOSc+rXr9+mTRuCUDEMrwhV3IgRI/T19alpHR2dUaNGEYT+geEVoYpzcHBo3LgxNW1lZdW6dWuC0D8wvCL0Q4YPHw4JrLa29siRIwlCJeCRA0j+iEXStERhToaYECVS3bRVrBwadC0oKKih1SQqREAYQFObo2fCVeVi8lTN8LhXJGeeXEl7+yybw1HWNeKKhFKC/oujqpSdJhIWSK0dNFv9bEBQ9cHwiuRJoG+KsKDQsbMRQV/z4maqRCTp0N+YoGqC4RXJjUeXU/MEhc3cDAn6NsG304hU2tYD/2LVA6szSD7kZos/hOdhbP0u9i76yXEFWakigqoDhlckH9IShESp+juy5I6SshJ0AxJUHfDIASQfstPFBqY8gr6Tngmv+BALVA0wvCL5UCglwnw8TuC7ifKlUglB1QLDK0II0QLDK0II0QLDK0II0QLDK0II0QLDK0II0QLDK0II0QLDK0II0QLDK0II0QLDK0II0QLDK0II0QLDK0II0QJHzEKoiixZOnfW7F8JUhiYvSJURZydXUUiHBtQgWB4RaiKuHbsQpAiweIAYq3Y2OhlXvN693Xz6NNp4e8zX70KouZ36972+InDsoet/dNrwsRh1HSPni4+/zsIrfgOro4wPX+hZ3ZONrVILBbv2r1l9NgB3d2df5s/7eHDe9T8qKgIeDDc7Teg67jxg/fu2wYPEIn+vUAAvJdbl1a5ubkliwMPHwXOmDkBPsnQ4R5/rFmSmppCzYeHrVi1CF6qSzcn+FTnzp+k5p8+c7xv/y73Am+5urXYum0dQfIAwytiJwhwnjPHczicNau3rv9zhwpHZeGiGfn5+eU/i8NROXnqWI8efW5ce7J2tTcE6K3ef1KLtmxde+q0T2+PgT7HLrg4uy5ZNvf2neswX1VVFW4PH907cMDwWTMXdWjfGULk48f3Za95997N1q3aaWhoyOaEvwubv2C6g0Pzg/tPTZs6NzIyfM3apdSieQumffr0cbnX+r+OX4JiwuYta96EhcJ8Lpebmyvw9T01f55X714DCJIHWBxA7BQfH5eenta3z2Brq4Zwd8ni1cEvn0MG+tUn1q9n3dyxFUzY2DTu1bMfZKNzZv0ulUoDrvgNGTyqp3tfWPRzt14hIcGHj+yBOKtUfIkaeEr/fkOpVzAzM4eQ2qaNC0xDWvr69St495JvEfIqSE1NbdjQMcrKyjVqmDRsYBP1PoIUp7SQYu/fe8LSsh7cHTpk9KPHgYcO7169ajO8C+wbBg0a+ZNDc4LkBGaviJ1MTMx0dfVWr1169Nh+CIUQyByaOmpqan71ifXrN5BN1zSzgCwY0snw8DdCobC5Y2vZoqb2zaAskJmVSd21tmokW+TWqdvdezckkqKLBNy5e0NdXb1tm/Yl38KucVOIlVB5gEz5Y9wHHR1d+Gww//37CAi7VGyVvezbt69ldxs2sCVIfmD2itgJWtObN+65eOkctOj37d8OGeWoEePd3H7+6hN5PDXZtJq6OtwKBDk5xRXYqdPHfvbg9LRUFZWijYjL+/c6YJ1cux06vOf5iyeQ0t67d7Ndu47UY2QgoV79x5Y7d67v3rN1+46NzX5qMWrkBDs7e0h11dTUSz4SSgp5ebklvxRB8gPDK2KtWrXq/DrRc/Soic+fP77s77tq9eLadepStYKSJP+9FhUEU9l0fl4e3ELIUykusM6aubBmTYuSDzY2NklLS/nsBc3Na9WrZxUYeMvaulFQ8DOIpOQLLVs4wT/4bM+ePTp95n8LFnqeOX2Vz+fn5+f958PkCgwNjAiSTxheETvFffr48uXzbl17QnPbycm5Zcs2XX9uA218CK9cLq9kSvjhQ0zJJwYHP5NNv4t4C4knhFRo6fOK81OqFQ+gsFtYWAjZZVpaKe8OHVx+fmdq166rra3zZbU0KOhZgbAAwquhoVGXLj2gjgG9cAmJ8Q2sbaBoAG9q9U+B4s2bkDolagVIvmDtFbETNOfX/um1Y+cmKG5CAD3mcwD6texs7UlxnxV0+kODH6aPHN2XkpJU8onJKUlQEoV4Ghsb7XfxTIcOnSGwQhiF9jv0ZUHXExRh4emz507atHl1We/evr0bhEt/f194OofD+WxpSGjw0mVzL/idychIf/0m5MzZ4xBnTWqYtmjhBEWMDRtWhr19nZaWCjUNCK8D+w8nSD5h9orYqYF1o5kzFhw8tOuvk0fhrmOzlhvW76xTpy5MT5k8e/36Fe692kNmOnDAcNeOXaF6IHtij+69Q0NfQkkUpiHxnDplDjV/0MAR9epZ+xw/CA/m8zVtbZrMmrWorHevaWYOH+Bt+JtpU+d+uXRA/2EQWL23rduwcRWUUzt26LJxw26qPrvCa/3OXZsmTR4J8+vWtVruta5x46YEySclaOAQhBjv9cOsD+/ynXoaEzr16u3at8/gEcPHEbZ4dCnZ2JzbpJ0OQVUOs1eEEKIFhleEEKIFhleE/nX+7HWCUCXB8IoQlvtMzQAAEABJREFUQrTAA7OQHEhISAgJCSHYC4vkCoZXxFBv3rzZu3fvhw8fYHr58uXh4eEEIbmC4RUxyKNHj5YuXfrsWdF5UwEBASKRSE9PD6a3bdvWp08fokQQkiNYe0XVRiAQ8Pn827dvHzlypGcxKAI0a9bMxsYGlnp6ehKE5Blmr6jq5OfnU43969evd+nS5fLlyzANEXby5Mnu7u4w3atXL5hQV1cnCMk/DK+IXtHR0c+fP4eJmzdvurq6PnjwgBSNqVr/2LFj/fr1g2lHR0cHBwdqUGqE2ATDK6pkEokkMDDQ19cXpiGYzpo1KyoqCqah1Q/zBwwoupBJ7dq1DQ0NCUKshuEVVQKxWHzixInNmzfDdGRkJExTw0Q1b9789OnTVJaqra1NKiooKOjMmTMEx8dAcgW7tlAFSaXS9evXx8TEeHt7Z2dnw0Tr1kXXSrG2tt6y5e8BpD8bpf97PXnyREtLq2HDhrdu3bK2bkGwgIDkCmav6Jvk5OQIhUKYmD9/PpRQoQIA4dXCwmLKlCkwU09Pb+7cue3atSOV4dOnT3C7ffv2ffv2UTmvp6dn058a89Rxdf1uXHVlrhr+3aoH/t1RmSIiIjIyMmBi2rRp3bt3z80tGuEfOvehvQ9tf8hMBw0aBKklqTzh4eHwRvfu3YPpUaNG7dy508zMjFpkYMr9+C6XoO/0KSJXr4YqQdUBx3tF/4I2/rNnz+oUmzp1alJS0qZNm0xNTePi4mrWrEnoAXXbPXv2fPz4ceXKldHR0WpqaiYmJqU+8uz2OKeexhpaGCy+lTBfcutEfJ+pNfHAjGqB4VXRQVyDymajRo2gZ3/FihXp6ekzZswwNzeHUgCt1yVNSEgICAgYOXJkcnLy+fPne/ToUVZUlUn9VHDlaFKPCRYEfZtL+z649DFS0cwpKCiAH5S6Bfn5+bBXa9++PUF0wvCqiN69e3fq1Ck7Ozt3d3cfH5/ExERo5kOWSuiXkpICmZSBgcGIESNatWo1adKk73p6RrLwf2s/tOhmqG3I1dJVKSzEpOxzkKfmZIqyUoRPAlL6TjM3NONBPQcqORBVoWIO2zsVYSG8ampqXr16lSDaYHhlP6iZamhohIWFbd26FUql0Oq/c+cONPw7dOgAYY5UCdiYYQuH3ipIVA8fPlyjRg1SUWKR9LF/Wvz7fGFBoTBXQhhAKBLBdsQrI9mH7w4xT+WLCxrSREWNw1NTMrVUa95Zn+rUgl7HmzdvfralQ88kdboHog+GV3aCIibUT9++fQubVvPmzRctWgThFfqpHBwcqAtKVxnoH4MCrqura+/evSMjI+vVY9tlpfPy8kaPHg0p+a5du0o9theqH8OHD/f39yfVB9oKoaGhJSuwsGeF4gxBdOIsXbqUIPlHjYgKNdOoqKguXbqIRKJ27dpBhtK1a1coa8IDDA0NYekPHoj67aDV+ejRo8aNG79588be3t7NzQ1m6uvrE9aBMsv169fT0tL4fH7TpqVc1RXmw2/B4XCqeMdWUqdOneAXga5L6i4kVS4uLtBdiefO0QoPzJJjT58+PX78OExA8XTs2LHU6fzQ7oaJBQsWwLSxsTGEVFKFIKTC7bNnzyDiQKYM023atKFON2Al6Cy6cOEC1VN06dIl6ji2L9WuXVtLS4tUH3h3SKRkwVRHRwd+l2XLlkHtm1ptEB0we5UzFy9ePHHiRNu2bWF7ho5+CKA//fSTurr6sGHDqCgG3f3KylW914QPA5kyfCp4d8jUzMzMIF0yMjIibHf69OkrV65AlxEpPqwNolipCSwYOHAgFLuhCE6qCXRdQvocHBwMu4T79+9bWVn17dsXAu7Ro0cPHToEH8za2pqgSoXZK6MV9YoQcuzYsQkTJkAJD6Zfv34NLW5oaUIgg2IfJK0wk1NV3SZf2r9/f8eOHSF9g7oe9J/Mnz+fKAyIU76+vnBL3YUfy8/PT9YA/0z37t0hzyXVqn///lClKTneY8uWLb29vVeuXAnNDlh05MgRgioPdm0xC+RB0NEPSdCOHTtgW4VOIcgyzp49a2Fh0axZM4YcHA51Xh8fH2hdwsYJRQBHR0dobBLFA5WZrVu3ysIrKd7PTZw4EXq6iByC8vHhw4chmYWWEHSFsbJQXsUwvFa/jx8/QtyEfgbYViF9gHzQzs4O6qpQNv3qkfZVKSIiIjU1FULqwYMHVVVVIRWi9bwD5uvTp090dPRnM+vUqVM0uFdpYmJi9PT0fmTksKoBKyHEWag1QZCtX78+QRWF4bUaCIXC58+fQxsNutS3bNkCCSBUwKEj6MOHD5ClEoZJSUmBCt3t27e3b9/+22+/QamXoP/at28f5LBfPUUiMDAQ6uay4cQYDqr8EGShgD58+HDYpxL0/TC8VhHoU7527ZqmpmbXrl0hO3j48OEvv/wC3SBQtVRTUyOMlJmZOXny5Lp163p5eVH9NgSV5hvDK1i3bh387nJUS3nw4AGsrrD2QpDt1q0bQd8DwyuNoDMKKll8Pn/8+PG3bt2CNbVnz562traE2c6fPw+94du2bUtPT4cya6NGjQgq17eHVzn19u1bCLJPnjyBcsHQoUMJ+jZ45ECloYZDhZA6a9asefPmwXRSUhK0rTp37gzT7du3h151xsZW+PDnzp2jBlp9//79hAkTSPEorhhbKxc0Vg4dOkTkTYMGDVasWHHs2LHExMTmzZtDfaOsI3xRSRhefwh13VNoRA8ZMoQ6Rgq6/t3d3amj+iGYQics9HUQphKLxXFxcaR4kOxXr15BPCXFY1c3adKEIBpAISg0NBSq7UQOQQl+5syZjx49guJG3759ocOAuooaKgsWB74PRFIIQ05OTlKp1NXVFeqSBw4cEAgE0PsPe3giVwICAhYvXrxz507q9CpUYd9VHEhNTY2Ojm7WrBmRcxcuXIC+L1NTU6gYODo6EvQFDK9fFx4e/uzZM8hJoWOqV69ekI1u2rQJ/m65ubkwh8gVKKdu3LhRQ0MDyheQesDugaAfxvraazkCAwMhyObk5ECQ7dKlC0El4KUMSwctoDt37gwcOLBWrVp79+41NjZWVS0aJB+6fagHKCkpyVFsffz4cXBwMPRZQymgZcuWVBcwxtbqcvHiRYhHsHYR+demWFhYGATZzZs3Dx8+fPDgwQQVw9prEercU6iITZ8+HbpHYfrly5cWFhYQVWF67dq1s2fPrsbhjioM6hjw1aCgARUMKysrmGNnZ9e9e/eqH5QAleTi4rJ9+3bCIg0bNly1ahWsZtT+29vbOysriyg8xS0OQDMZenIuXboELTtI67p27XrlyhVoNbdq1arKRu2jCTV+NvT+Q4sVvh0EU7zUEq0qUBzIz89XKUZYB/bo1HlfHTp0gIoBk7t26aZAWUxycnJERARMQEiF5gy0/WEa2v7r16+H2ArTnTt3btu2rVyv8Tdu3OjTp09MTAxML1++/ODBgxwOB2MrA8FqBhGWsBF8tdGjR9+8edPe3n7WrFmenp7QdUEUEpvDKyTmQUFBUHqH6cuXL0NVCCpEMA0d5VAHgE4qUtxYlve9q0AgOHToEDUaE0RS6LmiDlalKhuImSAGjRw58sshC9iEumZ73759d+3aBWnstWvXiIJhW3iFNpqfnx81yDTkp1u3boU+BFJ8VL+/vz81br+pqSljz0P9dmlpaXfv3oWJW7duQXUV8nGYhuZY7dq1CZIHUEx4+PAhYbt27drt3r37t99+u3r1KtT9T5w4QRQGG2qv0MiCmjqEm4ULF4aHhx87dqxTp07woxI2glyVz+dD83/cuHFTpkyhcnBUvRT5wKzvkpCQADXZs2fPQlMS8lm5O67xe8lreJVIJIsXL/7w4QP8WhBYz5w506JFC9afazR9+nT4yvBlqSBLEDNUOLy+ePHCyMioii/YU+2EQiHV9+Xm5gZBFvo/CEvJR3EgPj6eGrQY1mAIo6S4rgr56erVq0nxBfIglWNrbH3y5MmcOXPgLwDTgwcPpsYSxdjKGgp4NSYulzt27Njbt2/b2tpCxjBz5kzoIyFsxNzsFXobLS0tIXRCLyR0+kM5FZoSISEh0G9Tjdc+qTJQV9XV1W3cuDF0C1hZWXXs2JEgpvqR4gB0/ri6usJvTRQVxFnIZKE9OmbMGGdnZ8IiDA2vsM6dOnUKOqYMDQ0zMjIUcOUbMmTIhg0bGHW1AlSqiIiIefPmeXl52djYkAqBNfzTp08Vfjo7vHr1atOmTb/++iubhi9gaHiF3ipIUanzUBVTYmJijRo1IFu3s7MjiKkgIjx48AAa+D8yciMkbqNGjYLeHmrsSoU1cuRIqIOxaYVnaO1VTU1NkWMrgNgKt1AZuHXrFkHMExgY2KlTJwMDgxMnTvzgqLiQSUBXD7wUTAcHBxNF9fr1a5al8AwNr1u2bDl79ixReFAeoUbpRswBv8j8+fMhqp48eRJSTlJJqCEKX7x4MWvWLKJ43r59C30MLBsNg6FfBooDGFYoVINx6tSp1KmuqHpBr4CLi0uHDh0gA6BGH69cUCXo3bs3+WekdsXx5s0b9l0ag6Hhddq0aX369CHoH2vXrt22bRtB1Sc2Nnb06NGQZEGxldYiadu2beE2Ly9v6NCh2dnZRDGwrzJAcDhtuePr69uzZ0+Cqtb27duvXr26bNmyqjy8OiwsDHo4IVkmCgDKLFBywdprVcDaa1msra3xeshV6cmTJz///DOPx4MVsopPXWnYsCEVW6FcEBISQlgNigPsy14ZOvge1l7LApvcoUOHJBJJXFwci88mZAJo2C1ZsiQpKenAgQPUgRzVBT7A4cOH7ezsxGIxK4eIhTxd7i5V9y2w9ip/jI2NORxOamrq8uXLCaLHhQsXmjdv3rJly507d1ZvbAW6urqwRcDEjh07oG+NsA4rC68Ej3uVXw4ODo0bN46OjqauZIMqS0JCwoQJE549e/b06dPu3bsTJpk6dSr0rUFCLRKJCIuw8rABgrVXuebh4WFmZhYREXH9+nWCKsPevXvHjRv3yy+/MHaklQULFujo6ECE9fb2JmyB2WuVwtrrN+JyuVCNDQgICA0NJegHBAUF9erVC5oCfn5+DD/tHfrZatasyefzoSZLWAFScliNCevgmAMsERUVZWFhAdFBXV2doO+0YsUKKLNAxipfQ69Sw/7u3r0bMm75Pd8JUtc//vjjyJEjhHWw9soSdevWhT5lNzc3dl++qdL5+/u3atUKOuWhLCB3w1pTw/5CCZ66FqecYmvhlWDtlU2UlJTu3bv3/Plzgr5BamrqlClT7t69C380qGITudW6desrV67ABHwR6Jcj8oathVeCtVf2oQ5oW7x4MUFlO3jw4ODBg4cOHbpy5UrWHEkK5cuxY8fK3WAFYWFhrCy8Ejzula0GDhw4efJkgr4AfYD9+vXLzs6GjA/yPsIihoaGFy9elEgkpLinjsgDqVQaHh6O4bVKYe31B9na2lIH7ty+fVs2s02bNgMGDNE8kXAAABAASURBVCAKbE2xP//8c+rUqYSl6tSpQ4qHsjx16tRni37++WfCMCwuvBKsvbIYlGJJcefyggULYKJHjx4FBQWJiYm+vr5E8dy4caNdu3aWlpaHDx+GW8J2+/bts7CwgAnZYAUuLi5QmV24cCFhEnaHVxxzgOUgYTEwMIBKC9XpAY3iEydOKNSYW1lZWUuWLIECa0BAgIaGBlEYLVu2JMWXAtuxY0dsbCzsaJWVlaHnExo0zBmFC/q17O3tCUth7ZX9YDOTDcUNGxjEWX9/f6IYfHx8evXq1bt3bygIKFRslfHw8Bg+fHhcXBx1F5ovjBo4GIsD1QBrr5XI2dmZKhRQMjIyjh8/Ttju3bt3Q4YMiY+Pv3nzJssu7/y95s2bJzvpACYgkz148CBhAOiFi4yMtLa2JiyFtVeW69atW05OTmFhIXTRUnMg1EIye/XqVcJeGzdu/P3336EmoJjXrfrMZ5c8EIvF58+fZ8LpJ+xOXQnWXuVaQZ5UmC8t/zF/+Vzw8/ODVO7jx48pKSl5eXm5ubnifCnMb+XYgbBOWFiYl5dX//799+woOjYgO7304cSUlImmjpwd7pqVJi7RCPlWI0aMqGPeCPLEvGLUzOT4HO9N+2D3Q6pVSFCkjXWzsn4jJtPS+6aVh1ljDnTs2DEzM1P2kSDPgmkTE5NLly4RVMLTq2mhD7JUecqir4XXkgqLDzMEsLHBrQYbRycoEAq5qqpKX4tDujW4KXEFDRy12vYyJMyWmSp6dCkt8mVOTSuN9PgC8v2khf+AX/2f/2F+ta8AsB4qQbWiAjuNamVgxouLzK3fVBNWHp46p5xHMmsH7uTkBJG05OAUMO3u7k5QCf6HEjT1VTuPrKmpi+XpisvLESfE5B1eHjN0fi2OCkO38NR4oe/uTx0GmDTvZqSiyqqLVMs1YYE0LaHgwNLo4Qtr87XLjKLM+sEGDx5sZmZWco65uTnMJOgf/gcT9Ex49s4GGFt/kLqmiqWtVrt+NXzWxhJGykgW+e6K6+dZx8BMDWMro3B5yia11YcuqHfIK1oiLrMAwKzfzNbW1s7OTnYXmnhdu3bV1dUlqFj0a4GqOsemlR5BlcTQTM26mU7Q7XTCPI8up3YcbEYQg3UYZHrvXEpZSxm3S4RKvKHh3+UwSF0V/CTOzyR9KIB6K0GVSlNX5eO7fMI8EcE5ukZcghgMfqD3oYKyljJuW7WxsZFd7rhbt256epip/asgV2JoyiOoUumb8AjzxpTPTBHVasBX5shZt4+igRqdjiG3rAN4mJgKjRo1ysDAwMTEBFPXzwiyJGJWXcKOEaAXPS2RiUcBMvNToc8kxeaVdaTKjx458CkyNzNFLMgW52ZJpBIiFn/HcUJlM2jb4Fc+n//0cgEhieSH8dSVlYiShjYH/hmY8YzMMAFECNGuguE15o0g/HlOVIhAz0S9sFCJo8pRhn8cTmUdRWvXpD3cZgtIpcjJVSo61DNOLBHmi/IzRfmSek34DR21atRWIwghRI/vDq/x7/PunE1V1eAqqfDqtdZTUeUQeSPME6emCG6fS1fXIO08DLD3ACFEh+8Lr9f+l/wpKt/AUp+vJ8d5H1ddRd9CByaykgSnt35q1ELLqYcBQQihSvWtXVtikfSgV0y+hFfrJzO5jq0laRvz67W2SEpQPrstjiCEUKX6pvAqERfunh9lalND04BPWEe3praqjvbxdXJ2ATiEEMN9PbxKpYU75kbauFry+Kw9C1PTQEO7pv6hFTEEIYQqydfD67E/Yq2cahK209BV07fQvbgvniCEUGX4Sni9dTpF10KXx1eIvnUtY00R4QXdziAIIfTDyguvqZ8K3ocItIw0icLQNdO5dy6FUWPgIoTkVHnh9c65VENLfaJgTKz17p5LJQgh9GPKDK8J0XliibKWEUMvrhn06trs31vmCCp/HDnDOrpxUQUFeRKCvuB38WwHV0exWP6u3oGq3cePsbDyPHn6kCiMMsNrRLBAiaOoAzYrKUeH5hJU7Oy5v/5YU80XZfp28vVpEaNU+spTZniNfCnQMlbE68IDDX3+u6Acgoq9ffuayA/5+rSIUSp95Sn9pNj0JKG6lip9BwxEx768cnPvh4+vNfl6jRq07dxhnJpa0QkLgQ9PXr29/9cxOw4fn5+YFGVao76z0+DmP/WgnuXnv/Vp8CUeV8OhSRdjw1qENtrGGvGhWUT+vX8fOWbcQO8t+3fv3fry5QuTGqaDBo10aOr4+5LZ0FJr2NB26pQ5DRvYwCNzcnJOnjr6+MmD6OhIA31DJyeXMaN/VVNT85w5Pjj4OTzgypWLu3YepV42NTVl+coFoaEvzc1rDRo4ovvPHuV/jLJenBRfWnHzljX3Am9xVbmurl3tbO3nL/Q8fTJAX98AShD79m9/+OheUlKCnV3T3r0GtGrVlnpBjz6dRo+amJmZcejwbnV19eaOradMnm1gYFjy0x7cf7J2bUuiSJYsncvhcGrUMD1+4vCypWud23VMS0vdvmNDSGhwfn5+8+atRwwbZ2FRGx4Jnbenz/wvIMDvw8eY2rUsHR1bwS8Cz4VF8LPCXzUsLFRHV691q3YjR4zn8/8+mejM2RMPH9598yaEy+PZN/lp7NjJNc3MYf7pM8d9/ndghud8+AAeHgOmTp6dlZ21a9fmS5fP6+joOjZr+cu4qTVqmMg+5/oNK6HKBL8XfMJpU+d+9XuV9b7A98Lpv/46Am8H68bY0ZMGDemxaOFK145dYJF/wAVY+v59hKVl/Y4dOvftM5gaNnCZ1zyY6OTabfXapXl5uTY2jSeOn96okV3Jlefc2es62jrkh5WeveZkiPPzKmVowVKkpH7YdXCqSFQwZfzekUPWxCe+27H/V4mkqJzHUVHNy8s+d3HdAI8Ff3o9bGLX8a9zK9IzEmDR/cen7z8+1af7nOkTDhjomV29uY/QBv76OekiQZbcVxhVVYvKO97b1sFGcuPaE1s7+z17t27avPq3uUsDLt/ncXlbtq6lHnnmLGwhBwcOGL5q5aYJE6bfun0VtjGYv2nDbljzOnfufvP6U2urhjBHRUVli/fa4cPGbVi/EwI0vFpiYkL5H6OsFwcnTx274HcGovzOnUfV1TUgnpLi61fCLXy2U6d9ensM9Dl2wcXZdcmyubfvXJd9rxMnDsPDYDM4dOD0q5Cgg4d2ffZpFS22kuI/S9T7CPi3cvmGJo0dJBLJjFkTgoKfzfBcsH/vCT1d/UmTR8Z9+giPPHPm+NFj+/v1HXLcx8/dve/FS+cgIsP8j3EfZs+dlF+Q7731wPJl66Ki3s2YOZ4qtb96FbTV+09bW3svr3XzfluWnp62ctUi6n25XG5ursDX99T8eV6wF4THz5s/LSU1GdYQ+GWTkhPnLZgmq9cfOLizSZOfYNGA/sOgMX7j5pXyv1Q57/smLHTjpj9cXDodOXSmvXMnrxXzyT8rz7Xr/mvWLoM11ueo77ixk2FF8t6+nnoWrMChr19evXZp544jly/eg62AKgiUXHkqJbaSsrLX3CwJh7ahsJ4H+6twVEcNXsPnF11Eq3+vhas2eIS8uW1v50qKrs0rcuswrrZFY5h2bNo94PruuPhwPV2Tew/+amLrCgEX5kM+G/sxNDmVxivQcdU4gkxxOdeAlCOQFf7k0BwmYBW8ft2/Z89+No2KLmjm7OwKqQ0kMrA7gXUdQpgsJIWEBD9+cn/C+GlfvhpsJz3d+7Vs4QTTxsYm165dfhMWUjI3+VI5Lx5wxQ9SmPYunWB66JDRMJ96TEFBASwaMnhUT/e+cPfnbr3gWYeP7IHXoR5Qs6bFsKFjiqY0tSB7DQ9/QxQe/I4JCZ92bj9CtQyCgp7FxkavX7eD+vV/negZeP/26dM+kDAGv3zeoIFNly5F7cIe3Xs7ODTPyy3qbIBfU1VFFQIrZJ1wd/as3wcPdYe2BfxAkOUd2PcXtFcgPJGiQUhECxbNyMzKhEgE7wvZMTSMqDeCx0OmeejAqVq16sBdyJf/OnkU8mjqQ0Ljya1TN2oC9ruvXr2A1LKcL1XO+1654getHGjHwCInJ+fwd29ev35FPevSpXNNmjh4Tp8H03p6+qNHTly7zmvYkDEwDXPgy86ZvVhDo6j46dqxK6Sxubm51N3KVUZ4zRZzuHRFFqgMWJjbULEV6OuZGuibv48JosIrqFXTlprQUNeG27z8bAgBKWkfZFUCYG7WkNBJVZ2TK//ZK8XCog41wdcsOoS5rmV96q66mrpIJBIKhTweDxKfJ08frF6zJCIynEo0qBWxVNBAoyZ0dYou1VOQ/5ULVZX14pBeRUdHdevaU/ZI53auUMSACQiX8MEgbsoWNbVvdtnfl9qu4K61dSPZIi0tbYEAa+VFoKVPxVYAST385amQR4qDL/wNIbDCtJ2d/e49W9f+6QUxqHVrZ1lbOzQ0GFokVGwFJiamZmbmL1+9gPAKpYNPnz5u274e9qYCwd8jMWekp8kSvYYN/t5sIyPfQaiiYiuAFHLRghWk+MgBuG1s11T2aXW0dWE/Wu4XIuW8L+TpkG9SYZcUrzyHDu8hxRUnqIeMGP6L7EVg/wEz4YtQu2eLWnVkwVRTUwtus7Ozqi68AiXaLj+Ul5/zIe717N9blpyZlf3voaZfXlkhv0AglUp4vH+/P5erTugklRR9DsIKVHOprLsU2Nhghw8td4hokIru3bcNCmdlvB6RrdBK3/YnKuvFcwQ5sOPU0Ph3nCDZhp2Tkw23U6eP/eyl0tNSqe1ZiS2/TuWC6qRsGv6GsPvs4OpY8gG6ukV7RCgLwJ8dklloQcOv2b6924RfphkaGsFTwt6+/uwp6cWJZ2Dg7UWLZ0ELY8L46fXqWT199mjub1P+89bcv7tqYFfH45U5qB5H5fvytnLeFz4ttJ9kj5StPLBjhi8OhSaq1vTvF0lPoyZK3QToUPpX1dBWkYjounamlpaBZe2mXTqOLzmTzy+v2KHG4ysrc0QlPlKBkN4DpyRCCTsqA98CYtwFv9OwyUE7kZpDRTe6X1xDvWh/CVuC7MHp6X/vZQ0MjeB21syFUAQo+WolNydUPug7gn6/lSs2lpzJUS4q+kF8gZ8D/kHr4fnzxwcP74aYuGrFRn0Dw8aNm0Jzu+RTIMeEW79LZ2ER1DGpmeWsIRC4ocsIssVKiWLlvC8EcXGJlSc17e8LYkP+DqloZ7fuzv+UkihmpuakapURXrU4EhFdx9Wb1bB6Fnypbh0H2V8/ISnKyKC8IwEgVdHTNY2OfeXS5u85b94GEjoJ8yUa2vJ3IYaKgQCXl5dnaGhM3YWd//0Hd0glKefFoelqbFwjOjpS9mDIp6gJ85q1eMWJGFToqDmQehSnugp6sGAF1KtnDX952CHJ2v6f4uOoek5AgB9UVywt69WpUxf+ZedkX7x0tugpda2uXL0IxR/ZtgnxF+qeMJGVlWlSw1T24nfv3ijrfRs2sIFS7NvwN40y1krbAAAHk0lEQVQaFpULoP67YdOqqZPn8HgVucZdOe8Lu95378JkdwMDb5ES3x2+lGzlgZUwPj4OVjZStUrfvWjrq6hy6Wp8OTsNhj2b7+WNQmF+UnKMX4D3eu8h8YkR5T/L3q7Tq9c3g15dg+kbdw/HfAwhtJFKCzV1VRQne4VmHVTKoLIJ3cqZmRnQCQAFMqhGUaUuWImhp+L5iyeytlUlvrhTa2fYnp88fQih8+SpYzCfehaE0VEjJ0BfFnQcQ0S+fec69Ghv2rz6q28n+7TwXkSxNfupRYsWTuvWLU9MTIC/xrnzJyf+Otzf3xcWXb/hv3jpnPv370At++HDe3fv3bCztYf5/foNhW0TOtkhPn74ELNr95Yx4wZCiRMW1a9nDT/Ti6CnUD2HX4p6i4TEUkaYc3RsBb/C7t1b7t67CU+BXy05KbHCB3KU875tnFxiYt77/O8grDzwGFhVZM/6ZewUiLZQg4KvA/O9ls+fOXsirEjlv5ds5flqRfgblR5edQy54nxJfjYt1wHW0NCePcWHq6q+aefItVsGREU/7++x8KtdVZ1cRrds1uvcpfVQtIXUtWc3T1Lc8CQ0yEoU6Bkr1hlrvy9cpcZTGzW637ARHrBZjhs3Be727tspPuGTe/c+0HqYM3dyZNQ7UiHlvPjIEeMbN3aAatrwEb1hU4EaAimq7Rb98QcNHAHduz7HD7r3ar95yxpo2c2ateir7yX7tFRHioL7Y+UmF5eiI5Y8+nSCbvpOnbr16TOIFFVdFtWpXXfh7zM9erv+uX45xKmZMxbCfG0t7X17T0Cf54Rfh40Y1Tco+Nmc2b9TB+SNGTOpZQunRb/P7Ny1NcTreb8tgyx13vxp1677f/amUMxdt3a7tFC6eMkc+GXV1NX/WLVZRaWCyUo57+vcrmNvjwGHDu/u3dft7LkTsF6Rfw5GhHrC7p3HoJsUFsGOGUofK5Zv+Gr6LFt5Kqs4plRWhHpwMfVjdKFRXT2ieD6FJjV31bRy0CIM438owayepmVj9oxhBllSUlKCrJf5+InDx47tv+B7i1ShrDTR9WOfRiyqTZgkM0V0bsenPtOY9akYBfJZqF3Ur29N3X0TFjpp8sg9u3xkc6qGz6rIMV51VXmlNPfLrD3Xt+cXKurIHUpKEktbFl72hoEgno6fOPT0mePQer1x88pfJ4/27NmPIPQNXoUE/TJhCLRsEhLiX79+tXnzalvbJvXqWRHGKDNjNzJXU9cozEwU6NQoPdBkZCat8x5c6iJ1nmZeQenHIZoY1Z0yfg+pPItWupa1SCIRczilfMFa5rbjR24p61nJUemWNuoq3Co6dIMd5i/0DClR+Srp5589fp3oWdYTR40cn5mZfuWK3569W42MavT2GDh0yGiCFEmFVx7ouZo1cyGU9ceMG6CpqeXYrNXEiZ6MOmKvvIKIcx/DU5viygqvWpr6MycdKXUR9FlxuaUf+KasXMn9RWV9hqKPISrgqpZSbVFRKXMsBalEmvw+s//kegR9j9kzFwlFpVfqqaOvyjF92m8EKbAfWXmow8sIU5UX7HQMVBu11ExNztYyKqUKCYmhvp4ZqW6V+xmy4jPb9zck6DsZGOAfDVUQi1eerzSBnXoY5qbk5GbQdYoBo2TGZ2nypTYtK2c0B4SQgvt6hXHgTPPYFwmifJZ3c2Uk5OSl5XQaYkwQQqgyfFMHzoQ1dd8FfmBxDpuZkEPyBYNmWxCEEKok3xReoTNu0rr6WXFpWYmVdio6c6R/SOcq5Xn8Wv11ZIQQm3zH4UeQ3BkYSKIefsxKEhBWSI/LCrsVY9lApdsoHCgEIVTJvu8wqTbuBjYtte6cTU2JzC3kqGob8Xl8+Tt5NC+rIDs5V1pQYGim+vPS2jx1RRm6BSFUlb77KFQ9Y26vCaYJ0fnvgnIiXybyNFSkUiUOl8NR5SirQJyia5TYHwHFDbFIIhWKxUKJME/EU1e2aqpp/ZORrhFdFxNDCKEKHuRvUkcN/rXzMExLEGamFF2WSpAploilEjETwytXTUmZo8zX1tDQ5hjW5GrqKOoFxhFCVehHz6HSN+HCP4IQQui/8Mx6ecLXUeFg5l3ZlJWU9E0ZlyIUFhJD04qMP42qmHEt9bKKohhe5Yk6XzklrnIG+kUyqfH5ysy7cJeukWrsW4FYJCWIwbLTRNlpQlVe6YEUw6s8qVFbTVRA10V6FFZ2usi8Ab1XxqwYKwfN9ETcmzJaelKBZeMyBy/F8CpPLKw1IM96cTOVoEoSG5YT+yanSRtdwjxO7gbXfeIJYipoW9w8kdDOw6isByjRdD0VRJ87Z5NFwsJ6TbQNzNQIqqiMZGFSbG5kcHb/6eZKDKwOFMvJEB39I6bDIDNdI66GlqJc/I354HdJTyi4+VfCLyvrctXKTFIxvMqlkAeZofez8nMlBXlYm6sIAxNunkBi/ZNWiy76hNmE+dL7filRrwR6xtxkrLwzQI1aaumJwnr2/HLyVgqGVzkGPx1sewR9P2WOEn3XQqZJvkDC2CxbsRQW8jS+6VRPDK8IIUQLrOYghBAtMLwihBAtMLwihBAtMLwihBAtMLwihBAtMLwihBAt/g8AAP//+eRwLQAAAAZJREFUAwA26nnPMnGE+wAAAABJRU5ErkJggg==", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, Image\n", "\n", "display(Image(supervisor.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "3a94c3d1-3617-4d68-ac9a-b9bb2c05a604", "metadata": {}, "source": ["Let's now run it with a query that requires both agents:\n", "\n", "* research agent will look up the necessary GDP information\n", "* math agent will perform division to find the percentage of NY state GDP, as requested"]}, {"cell_type": "code", "execution_count": 11, "id": "89b77171-58d0-4b36-8cb3-b69d69e4bdf9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Update from node supervisor:\n", "\n", "\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: transfer_to_research_agent\n", "\n", "Successfully transferred to research_agent\n", "\n", "\n", "Update from node research_agent:\n", "\n", "\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: transfer_back_to_supervisor\n", "\n", "Successfully transferred back to supervisor\n", "\n", "\n", "Update from node supervisor:\n", "\n", "\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: transfer_to_math_agent\n", "\n", "Successfully transferred to math_agent\n", "\n", "\n", "Update from node math_agent:\n", "\n", "\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: transfer_back_to_supervisor\n", "\n", "Successfully transferred back to supervisor\n", "\n", "\n", "Update from node supervisor:\n", "\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: supervisor\n", "\n", "In 2024, the US GDP was $29.18 trillion and New York State's GDP was $2.297 trillion. New York State accounted for approximately 7.87% of the total US GDP in 2024.\n", "\n", "\n"]}], "source": ["for chunk in supervisor.stream(\n", "    {\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"find US and New York state GDP in 2024. what % of US GDP was New York state?\",\n", "            }\n", "        ]\n", "    },\n", "):\n", "    pretty_print_messages(chunk, last_message=True)\n", "\n", "final_message_history = chunk[\"supervisor\"][\"messages\"]"]}, {"cell_type": "markdown", "id": "599b8d13-70a9-40f3-99b9-5e3d35cc6f56", "metadata": {}, "source": ["## 3. Create supervisor from scratch"]}, {"cell_type": "markdown", "id": "e23764f8-f818-45f3-a5ee-4cb7b7002336", "metadata": {}, "source": ["Let's now implement this same multi-agent system from scratch. We will need to:\n", "\n", "1. Set up how the supervisor [communicates](#set-up-agent-communication) with individual agents\n", "2. Create the [supervisor agent](#create-supervisor-agent)\n", "3. [Co<PERSON><PERSON>]() supervisor and worker agents into a single multi-agent graph."]}, {"cell_type": "markdown", "id": "78d32089-0259-4aa3-a473-077ecce342dd", "metadata": {}, "source": ["### Set up agent communication"]}, {"cell_type": "markdown", "id": "a6c6b436-e95b-476b-b427-7b02d8384cc5", "metadata": {}, "source": ["We will need to define a way for the supervisor agent to communicate with the worker agents. A common way to implement this in multi-agent architectures is using **handoffs**, where one agent *hands off* control to another. Handoffs allow you to specify:\n", "\n", "- **destination**: target agent to transfer to\n", "- **payload**: information to pass to that agent\n", "\n", "We will implement handoffs via **handoff tools** and give these tools to the supervisor agent: when the supervisor calls these tools, it will hand off control to a worker agent, passing the full message history to that agent."]}, {"cell_type": "code", "execution_count": 12, "id": "7873a6c3-7de0-46d1-96ce-2dca7c62245a", "metadata": {}, "outputs": [], "source": ["from typing import Annotated\n", "from langchain_core.tools import tool, InjectedToolCallId\n", "from langgraph.prebuilt import InjectedState\n", "from langgraph.graph import StateGraph, START, MessagesState\n", "from langgraph.types import Command\n", "\n", "\n", "def create_handoff_tool(*, agent_name: str, description: str | None = None):\n", "    name = f\"transfer_to_{agent_name}\"\n", "    description = description or f\"Ask {agent_name} for help.\"\n", "\n", "    @tool(name, description=description)\n", "    def handoff_tool(\n", "        state: Annotated[MessagesState, InjectedState],\n", "        tool_call_id: Annotated[str, InjectedToolCallId],\n", "    ) -> Command:\n", "        tool_message = {\n", "            \"role\": \"tool\",\n", "            \"content\": f\"Successfully transferred to {agent_name}\",\n", "            \"name\": name,\n", "            \"tool_call_id\": tool_call_id,\n", "        }\n", "        # highlight-next-line\n", "        return Command(\n", "            # highlight-next-line\n", "            goto=agent_name,  # (1)!\n", "            # highlight-next-line\n", "            update={**state, \"messages\": state[\"messages\"] + [tool_message]},  # (2)!\n", "            # highlight-next-line\n", "            graph=Command.PARENT,  # (3)!\n", "        )\n", "\n", "    return handoff_tool\n", "\n", "\n", "# Handoffs\n", "assign_to_research_agent = create_handoff_tool(\n", "    agent_name=\"research_agent\",\n", "    description=\"Assign task to a researcher agent.\",\n", ")\n", "\n", "assign_to_math_agent = create_handoff_tool(\n", "    agent_name=\"math_agent\",\n", "    description=\"Assign task to a math agent.\",\n", ")"]}, {"cell_type": "markdown", "id": "3bc810a4-1866-4915-abd9-12655954e673", "metadata": {}, "source": ["1. Name of the agent or node to hand off to.\n", "2. Take the agent's messages and add them to the parent's state as part of the handoff. The next agent will see the parent state.\n", "3. Indicate to <PERSON>G<PERSON><PERSON> that we need to navigate to agent node in a **parent** multi-agent graph."]}, {"cell_type": "markdown", "id": "6a7bd301-c954-4189-87e2-b3bf42d187c1", "metadata": {}, "source": ["### Create supervisor agent"]}, {"cell_type": "markdown", "id": "ca787ce6-46f9-49c3-8f9f-ddb7ff52cdeb", "metadata": {}, "source": ["Then, let's create the supervisor agent with the handoff tools we just defined. We will use the prebuilt [`create_react_agent`][langgraph.prebuilt.chat_agent_executor.create_react_agent]:"]}, {"cell_type": "code", "execution_count": 13, "id": "de5d1ff8-e7a0-4efe-93fe-d304eaef29b6", "metadata": {}, "outputs": [], "source": ["supervisor_agent = create_react_agent(\n", "    model=\"openai:gpt-4.1\",\n", "    tools=[assign_to_research_agent, assign_to_math_agent],\n", "    prompt=(\n", "        \"You are a supervisor managing two agents:\\n\"\n", "        \"- a research agent. Assign research-related tasks to this agent\\n\"\n", "        \"- a math agent. Assign math-related tasks to this agent\\n\"\n", "        \"Assign work to one agent at a time, do not call agents in parallel.\\n\"\n", "        \"Do not do any work yourself.\"\n", "    ),\n", "    name=\"supervisor\",\n", ")"]}, {"cell_type": "markdown", "id": "dd4bc4e2-e133-42fd-a47f-6fb63fa51697", "metadata": {}, "source": ["### Create multi-agent graph"]}, {"cell_type": "markdown", "id": "8e6dcae1-eb91-4a07-aef8-53cbed14a002", "metadata": {}, "source": ["Putting this all together, let's create a graph for our overall multi-agent system. We will add the supervisor and the individual agents as [subgraph](../../../concepts/low_level#subgraphs) nodes."]}, {"cell_type": "code", "execution_count": 14, "id": "584443af-587a-44f3-a320-f942868191e4", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import END\n", "\n", "# Define the multi-agent supervisor graph\n", "supervisor = (\n", "    StateGraph(MessagesState)\n", "    # NOTE: `destinations` is only needed for visualization and doesn't affect runtime behavior\n", "    .add_node(supervisor_agent, destinations=(\"research_agent\", \"math_agent\", END))\n", "    .add_node(research_agent)\n", "    .add_node(math_agent)\n", "    .add_edge(START, \"supervisor\")\n", "    # always return back to the supervisor\n", "    .add_edge(\"research_agent\", \"supervisor\")\n", "    .add_edge(\"math_agent\", \"supervisor\")\n", "    .compile()\n", ")"]}, {"cell_type": "markdown", "id": "12576211-a946-46ec-bf81-24215597a1fe", "metadata": {}, "source": ["Notice that we've added explicit [edges](../../../concepts/low_level#edges) from worker agents back to the supervisor — this means that they are guaranteed to return control back to the supervisor. If you want the agents to respond directly to the user (i.e., turn the system into a router, you can remove these edges)."]}, {"cell_type": "code", "execution_count": 15, "id": "0175fe14-5854-4197-b7e8-559335d0f81b", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, Image\n", "\n", "display(Image(supervisor.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "d36496de-7121-4c49-8cb6-58c943c66628", "metadata": {}, "source": ["With the multi-agent graph created, let's now run it!"]}, {"cell_type": "code", "execution_count": 16, "id": "56ba78e9-d9c1-457c-a073-d606d5d3e013", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Update from node supervisor:\n", "\n", "\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: transfer_to_research_agent\n", "\n", "Successfully transferred to research_agent\n", "\n", "\n", "Update from node research_agent:\n", "\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: research_agent\n", "\n", "- US GDP in 2024 is projected to be about $28.18 trillion USD (Statista; CBO projection).\n", "- New York State's nominal GDP for 2024 is estimated at approximately $2.16 trillion USD (various economic reports).\n", "- New York State's share of US GDP in 2024 is roughly 7.7%.\n", "\n", "Sources:\n", "- https://www.statista.com/statistics/216985/forecast-of-us-gross-domestic-product/\n", "- https://nyassembly.gov/Reports/WAM/2025economic_revenue/2025_report.pdf?v=1740533306\n", "\n", "\n", "Update from node supervisor:\n", "\n", "\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: transfer_to_math_agent\n", "\n", "Successfully transferred to math_agent\n", "\n", "\n", "Update from node math_agent:\n", "\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: math_agent\n", "\n", "US GDP in 2024: $28.18 trillion\n", "New York State GDP in 2024: $2.16 trillion\n", "Percentage of US GDP from New York State: 7.67%\n", "\n", "\n", "Update from node supervisor:\n", "\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: supervisor\n", "\n", "Here are your results:\n", "\n", "- 2024 US GDP (projected): $28.18 trillion USD\n", "- 2024 New York State GDP (estimated): $2.16 trillion USD\n", "- New York State's share of US GDP: approximately 7.7%\n", "\n", "If you need the calculation steps or sources, let me know!\n", "\n", "\n"]}], "source": ["for chunk in supervisor.stream(\n", "    {\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"find US and New York state GDP in 2024. what % of US GDP was New York state?\",\n", "            }\n", "        ]\n", "    },\n", "):\n", "    pretty_print_messages(chunk, last_message=True)\n", "\n", "final_message_history = chunk[\"supervisor\"][\"messages\"]"]}, {"cell_type": "markdown", "id": "dac583a8-af2c-402f-801e-15a6bc318e44", "metadata": {}, "source": ["Let's examine the full resulting message history:"]}, {"cell_type": "code", "execution_count": 17, "id": "1eff504c-7437-477e-ab28-936c1238<PERSON>ca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "find US and New York state GDP in 2024. what % of US GDP was New York state?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: supervisor\n", "Tool Calls:\n", "  transfer_to_research_agent (call_KlGgvF5ahlAbjX8d2kHFjsC3)\n", " Call ID: call_KlGgvF5ahlAbjX8d2kHFjsC3\n", "  Args:\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: transfer_to_research_agent\n", "\n", "Successfully transferred to research_agent\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: research_agent\n", "Tool Calls:\n", "  tavily_search (call_ZOaTVUA6DKrOjWQldLhtrsO2)\n", " Call ID: call_ZOaTVUA6DKrOjWQldLhtrsO2\n", "  Args:\n", "    query: US GDP 2024 estimate or actual\n", "    search_depth: advanced\n", "  tavily_search (call_QsRAasxW9K03lTlqjuhNLFbZ)\n", " Call ID: call_QsRAasxW9K03lTlqjuhNLFbZ\n", "  Args:\n", "    query: New York state GDP 2024 estimate or actual\n", "    search_depth: advanced\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: tavily_search\n", "\n", "{\"query\": \"US GDP 2024 estimate or actual\", \"follow_up_questions\": null, \"answer\": null, \"images\": [], \"results\": [{\"url\": \"https://www.advisorperspectives.com/dshort/updates/2025/05/29/gdp-gross-domestic-product-q1-2025-second-estimate\", \"title\": \"Q1 GDP Second Estimate: Real GDP at -0.2%, Higher Than Expected\", \"content\": \"> Real gross domestic product (GDP) decreased at an annual rate of 0.2 percent in the first quarter of 2025 (January, February, and March), according to the second estimate released by the U.S. Bureau of Economic Analysis. In the fourth quarter of 2024, real GDP increased 2.4 percent. The decrease in real GDP in the first quarter primarily reflected an increase in imports, which are a subtraction in the calculation of GDP, and a decrease in government spending. These movements were partly [...] by [<PERSON>](https://www.advisorperspectives.com/search?author=Harry%20M<PERSON>ysky) of [QuantStreet Capital](https://www.advisorperspectives.com/firm/quantstreet-capital), 10/14/2024\\n\\n#### [The Worst of <PERSON>](https://www.advisorperspectives.com/articles/2024/10/21/worst-of-allan-roth/)\\n\\n by [<PERSON>](https://www.advisorperspectives.com/search?author=<PERSON>%20Roth), 10/21/2024 [...] by [Juan Pablo Spinetto](https://www.advisorperspectives.com/search?author=Juan%20Pablo%20Spinetto) of [Bloomberg News](https://www.advisorperspectives.com/firm/bloomberg-news), 04/05/2025\\n\\n#### [Your Clients Are Clueless About Their Risk Tolerance](https://www.advisorperspectives.com/articles/2024/10/23/clients-clueless-about-risk-tolerance/)\\n\\n by [Dan Solin](https://www.advisorperspectives.com/search?author=Dan%20Solin), 10/23/2024\", \"score\": 0.78650844, \"raw_content\": null}, {\"url\": \"https://www.statista.com/statistics/216985/forecast-of-us-gross-domestic-product/\", \"title\": \"GDP forecast U.S. 2034 - Statista\", \"content\": \"[](https://www.statista.com/statistics/216985/forecast-of-us-gross-domestic-product/#statisticContainer) This graph shows a forecast of the gross domestic product of the United States of America for fiscal years 2024 to 2034. GDP refers to the market value of all final goods and services produced within a country in a given period. According to the CBO, the United States GDP will increase steadily over the next decade from 28.18 trillion U.S. dollars in 2023 to 41.65 trillion U.S. dollars in [...] US Congressional Budget Office. (February 7, 2024). Forecast of the gross domestic product of the United States from fiscal year 2024 to fiscal year 2034 (in billion U.S. dollars) [Graph]. In Statista. Retrieved May 20, 2025, from https://www.statista.com/statistics/216985/forecast-of-us-gross-domestic-product/ [...] US Congressional Budget Office. (2024). Forecast of the gross domestic product of the United States from fiscal year 2024 to fiscal year 2034 (in billion U.S. dollars). Statista. Statista Inc.. Accessed: May 20, 2025. https://www.statista.com/statistics/216985/forecast-of-us-gross-domestic-product/\", \"score\": 0.7576693, \"raw_content\": null}, {\"url\": \"https://www.bea.gov/data/gdp/gross-domestic-product\", \"title\": \"Gross Domestic Product | U.S. Bureau of Economic Analysis (BEA)\", \"content\": \"Real gross domestic product (GDP) decreased at an annual rate of 0.2 percent in the first quarter of 2025 (January, February, and March), according to the second estimate released by the U.S. Bureau of Economic Analysis. In the fourth quarter of 2024, real GDP increased 2.4 percent. The decrease in real GDP in the first quarter primarily reflected an increase in imports, which are a subtraction in the calculation of GDP, and a decrease in government spending. These movements were partly offset\", \"score\": 0.75723875, \"raw_content\": null}], \"response_time\": 2.48}\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: tavily_search\n", "\n", "{\"query\": \"New York state GDP 2024 estimate or actual\", \"follow_up_questions\": null, \"answer\": null, \"images\": [], \"results\": [{\"url\": \"https://comptroller.nyc.gov/reports/annual-state-of-the-citys-economy-and-finances-2024/\", \"title\": \"Annual State of the City's Economy and Finances 2024\", \"content\": \"[[1]](https://comptroller.nyc.gov/reports/annual-state-of-the-citys-economy-and-finances-2024/#_ftnref1) At that time, the Comptroller’s Office expected five-year cumulative real GDP growth, 2020 to 2024, of 8.7 percent while the mayor expected 9.5 percent.\\n\\n[[2]](https://comptroller.nyc.gov/reports/annual-state-of-the-citys-economy-and-finances-2024/#_ftnref2) U.S. Bureau of Labor Statistics Local Area Unemployment Statistics (LAUS). [...] [[19]](https://comptroller.nyc.gov/reports/annual-state-of-the-citys-economy-and-finances-2024/#_ftnref19) FY 2024 actuals include $742 million in transfers to the capital fund through Interfund Agreements, as detailed in Schedule G6 of the FY 2024 Annual Comprehensive Financial Report. [...] (constant 2019 dollars)**22,550 15,322 13,596 19,451 21,211 21,096\\n**_% change_**(32.1%)(11.3%)43.1%9.0%(0.5%)\\n\\nSource: NY State Department of Taxation and Finance and Office of the New York City Comptroller\", \"score\": 0.82795376, \"raw_content\": null}], \"response_time\": 2.76}\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: research_agent\n", "Tool Calls:\n", "  tavily_search (call_DxuGWv9Ii19fPys582KcSLOg)\n", " Call ID: call_DxuGWv9Ii19fPys582KcSLOg\n", "  Args:\n", "    query: New York state GDP 2024 (in dollars, not percentage growth or projections)\n", "    search_depth: advanced\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: tavily_search\n", "\n", "{\"query\": \"New York state GDP 2024 (in dollars, not percentage growth or projections)\", \"follow_up_questions\": null, \"answer\": null, \"images\": [], \"results\": [{\"url\": \"https://comptroller.nyc.gov/reports/annual-state-of-the-citys-economy-and-finances-2024/\", \"title\": \"Annual State of the City's Economy and Finances 2024\", \"content\": \"(constant 2019 dollars)**22,550 15,322 13,596 19,451 21,211 21,096\\n**_% change_**(32.1%)(11.3%)43.1%9.0%(0.5%)\\n\\nSource: NY State Department of Taxation and Finance and Office of the New York City Comptroller [...] | Retail Trade | 301,565 | $56,208 | 14.4 | (0.6) | (5.2) | 2.7 | 2.9 | 2.2 | 2.1 |\\n| Leisure & Hospitality | 431,227 | $56,086 | 96.2 | 32.4 | 15.4 | 8.9 | 3.8 | 4.5 | 7.3 |\\n| **Total: Low Wage Sectors** | **1,997,491** | **$62,185** | **184.6** | **102.2** | **91.6** | **62.7** | **32.0** | **26.8** | **29.3** |\\n| **Total NYC Employment** | **4,485,695** | **$114,294** | **307.7** | **119.9** | **78.1** | **88.0** | **65.1** | **53.2** | **51.4** | [...] [[1]](https://comptroller.nyc.gov/reports/annual-state-of-the-citys-economy-and-finances-2024/#_ftnref1) At that time, the Comptroller’s Office expected five-year cumulative real GDP growth, 2020 to 2024, of 8.7 percent while the mayor expected 9.5 percent.\\n\\n[[2]](https://comptroller.nyc.gov/reports/annual-state-of-the-citys-economy-and-finances-2024/#_ftnref2) U.S. Bureau of Labor Statistics Local Area Unemployment Statistics (LAUS).\", \"score\": 0.8098936, \"raw_content\": null}, {\"url\": \"https://nyassembly.gov/Reports/WAM/2025economic_revenue/2025_report.pdf?v=1740533306\", \"title\": \"[PDF] New York State Economic and Revenue Report\", \"content\": \"Sources: Federal Reserve; NYS Assembly Ways and Means Committee staff. NYS ASSEMBLY| U.S. ECONOMIC FORECAST AT A GLANCE| 15 Actual Actual Actual Forecast Forecast 2022 2023 2024 2025 2026 Real GDP 2.5 2.9 2.8 2.4 2.1 Consumption 3.0 2.5 2.8 2.7 2.1 Investment 6.0 0.1 4.0 3.1 3.8 Exports 7.5 2.8 3.2 2.4 1.1 Imports 8.6 (1.2) 5.4 3.5 1.3 Government (1.1) 3.9 3.4 1.5 0.1 Federal (3.2) 2.9 2.5 1.7 0.2 State and Local 0.2 4.4 3.9 1.4 0.1 Personal Income 3.1 5.9 5.5 5.0 4.9 Wages & Salaries 7.8 5.4 [...] in 2024 on an annualized basis. National output, measured with inflation-adjusted Gross Domestic Product (or real GDP), continues to grow at solid rates, surpassing its pre-pandemic peak by 12.1 percent, as of the fourth quarter of 2024 (see Figure 2). However, the pace of average quarterly growth has decelerated from 3.2 percent in 2023 to 2.5 percent in 2024 on an annualized basis. 121.7 114.5 115.3 112.1 109.2 106.3 70 75 80 85 90 95 100 105 110 115 120 125 130 2019Q4 2020Q2 2020Q4 2021Q2 [...] three-month moving average of 2.5 million in December 2023 to 872,000 in December 2024. The ratio of job openings to unemployed persons, another key metric for assessing the balance between labor demand and supply, has also steadily declined in recent months. By December 2024, this ratio had dropped to 1.10, down from its peak of 2.03 in March 2022. 24 | U.S. ECONOMIC FORECAST ANALYSIS | NYS ASSEMBLY With diminishing supply shortages in the labor market, growth of labor and other incomes will\", \"score\": 0.8051581, \"raw_content\": null}], \"response_time\": 2.86}\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: research_agent\n", "Tool Calls:\n", "  tavily_search (call_wjYTJR9EnmCqzKrkrONpxYO0)\n", " Call ID: call_wjYTJR9EnmCqzKrkrONpxYO0\n", "  Args:\n", "    query: New York State GDP (nominal) in US dollars for 2024 (actual or forecast)\n", "    search_depth: advanced\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: tavily_search\n", "\n", "{\"query\": \"New York State GDP (nominal) in US dollars for 2024 (actual or forecast)\", \"follow_up_questions\": null, \"answer\": null, \"images\": [], \"results\": [{\"url\": \"https://nyassembly.gov/Reports/WAM/2025economic_revenue/2025_report.pdf?v=1740533306\", \"title\": \"[PDF] New York State Economic and Revenue Report\", \"content\": \"Sources: Federal Reserve; NYS Assembly Ways and Means Committee staff. NYS ASSEMBLY| U.S. ECONOMIC FORECAST AT A GLANCE| 15 Actual Actual Actual Forecast Forecast 2022 2023 2024 2025 2026 Real GDP 2.5 2.9 2.8 2.4 2.1 Consumption 3.0 2.5 2.8 2.7 2.1 Investment 6.0 0.1 4.0 3.1 3.8 Exports 7.5 2.8 3.2 2.4 1.1 Imports 8.6 (1.2) 5.4 3.5 1.3 Government (1.1) 3.9 3.4 1.5 0.1 Federal (3.2) 2.9 2.5 1.7 0.2 State and Local 0.2 4.4 3.9 1.4 0.1 Personal Income 3.1 5.9 5.5 5.0 4.9 Wages & Salaries 7.8 5.4 [...] New York State Economic Outlook (Percent Change) Actual Estimate Forecast Forecast 2023-24 2024-25 2025-26 2026-27 Employment Percent Change 1.8 1.5 1.0 0.8 Level (Thousands) 9,441.6 9,589.7 9,694.3 9,773.6 Personal Income Percent Change 5.2 5.8 4.6 4.4 Level (Billions) 1,581.5 1,671.1 1,754.9 1,835.0 Total Wages Percent Change 4.2 6.7 4.4 4.0 Level (Billions) 864.0 915.9 964.3 1,005.1 Base Wages Percent Change 5.1 5.4 4.4 4.3 Level (Billions) 764.0 803.1 840.7 876.6 Variable Compensation [...] is 0.3 percentage point above the forecast of the Division of the Budget. The Committee’s forecast is 0.1 percentage point above S&P Global forecast and Moody’s Analytics’ projection. The forecast is 0.2 percentage point above the Blue Chip Consensus forecast. Actual Estimate Forecast Forecast 2023 2024 2025 2026 Ways and Means 2.9 2.8 2.4 2.1 Division of the Budget 2.9 2.8 2.1 2.0 Blue Chip Consensus 2.9 2.8 2.2 2.0 Moody's Analytics 2.9 2.8 2.3 1.6 S&P Global 2.9 2.8 2.3 2.0 Sources: NYS\", \"score\": 0.846159, \"raw_content\": null}, {\"url\": \"https://comptroller.nyc.gov/reports/annual-state-of-the-citys-economy-and-finances-2024/\", \"title\": \"Annual State of the City's Economy and Finances 2024\", \"content\": \"(constant 2019 dollars)**22,550 15,322 13,596 19,451 21,211 21,096\\n**_% change_**(32.1%)(11.3%)43.1%9.0%(0.5%)\\n\\nSource: NY State Department of Taxation and Finance and Office of the New York City Comptroller [...] [[1]](https://comptroller.nyc.gov/reports/annual-state-of-the-citys-economy-and-finances-2024/#_ftnref1) At that time, the Comptroller’s Office expected five-year cumulative real GDP growth, 2020 to 2024, of 8.7 percent while the mayor expected 9.5 percent.\\n\\n[[2]](https://comptroller.nyc.gov/reports/annual-state-of-the-citys-economy-and-finances-2024/#_ftnref2) U.S. Bureau of Labor Statistics Local Area Unemployment Statistics (LAUS). [...] | Retail Trade | 301,565 | $56,208 | 14.4 | (0.6) | (5.2) | 2.7 | 2.9 | 2.2 | 2.1 |\\n| Leisure & Hospitality | 431,227 | $56,086 | 96.2 | 32.4 | 15.4 | 8.9 | 3.8 | 4.5 | 7.3 |\\n| **Total: Low Wage Sectors** | **1,997,491** | **$62,185** | **184.6** | **102.2** | **91.6** | **62.7** | **32.0** | **26.8** | **29.3** |\\n| **Total NYC Employment** | **4,485,695** | **$114,294** | **307.7** | **119.9** | **78.1** | **88.0** | **65.1** | **53.2** | **51.4** |\", \"score\": 0.7992077, \"raw_content\": null}], \"response_time\": 2.65}\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: research_agent\n", "\n", "- US GDP in 2024 is projected to be about $28.18 trillion USD (Statista; CBO projection).\n", "- New York State's nominal GDP for 2024 is estimated at approximately $2.16 trillion USD (various economic reports).\n", "- New York State's share of US GDP in 2024 is roughly 7.7%.\n", "\n", "Sources:\n", "- https://www.statista.com/statistics/216985/forecast-of-us-gross-domestic-product/\n", "- https://nyassembly.gov/Reports/WAM/2025economic_revenue/2025_report.pdf?v=1740533306\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: supervisor\n", "Tool Calls:\n", "  transfer_to_math_agent (call_WEBMqUupSKqT5slb6ilX2VEO)\n", " Call ID: call_WEBMqUupSKqT5slb6ilX2VEO\n", "  Args:\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: transfer_to_math_agent\n", "\n", "Successfully transferred to math_agent\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: math_agent\n", "Tool Calls:\n", "  divide (call_fsn6la6HTqfNl3sLp9v1WVoz)\n", " Call ID: call_fsn6la6HTqfNl3sLp9v1WVoz\n", "  Args:\n", "    a: 2.16\n", "    b: 28.18\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: divide\n", "\n", "0.0766501064584812\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: math_agent\n", "\n", "US GDP in 2024: $28.18 trillion\n", "New York State GDP in 2024: $2.16 trillion\n", "Percentage of US GDP from New York State: 7.67%\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: supervisor\n", "\n", "Here are your results:\n", "\n", "- 2024 US GDP (projected): $28.18 trillion USD\n", "- 2024 New York State GDP (estimated): $2.16 trillion USD\n", "- New York State's share of US GDP: approximately 7.7%\n", "\n", "If you need the calculation steps or sources, let me know!\n"]}], "source": ["for message in final_message_history:\n", "    message.pretty_print()"]}, {"cell_type": "markdown", "id": "239700cd-d2d3-44be-8cf5-021d15615de2", "metadata": {}, "source": ["!!! Important\n", "\n", "    You can see that the supervisor system appends **all** of the individual agent messages (i.e., their internal tool-calling loop) to the full message history. This means that on every supervisor turn, supervisor agent sees this full history. If you want more control over:\n", "\n", "    * **how inputs are passed to agents**: you can use LangGraph [`Send()`][langgraph.types.Send] primitive to directly send data to the worker agents during the handoff. See the [task delegation](#create-delegation-tasks) example below\n", "    * **how agent outputs are added**: you can control how much of the agent's internal message history is added to the overall supervisor message history by wrapping the agent in a separate node function:\n", "\n", "        ```python\n", "        def call_research_agent(state):\n", "            # return agent's final response,\n", "            # excluding inner monologue\n", "            response = research_agent.invoke(state)\n", "            # highlight-next-line\n", "            return {\"messages\": response[\"messages\"][-1]}\n", "        ```"]}, {"cell_type": "markdown", "id": "1c2a0aff-935a-46b8-bdcf-7125b80f1f41", "metadata": {}, "source": ["## 4. Create delegation tasks"]}, {"cell_type": "markdown", "id": "1a13d919-49bf-4cc3-838f-714cd3fb8b26", "metadata": {}, "source": ["So far the individual agents relied on **interpreting full message history** to determine their tasks. An alternative approach is to ask the supervisor to **formulate a task explicitly**. We can do so by adding a `task_description` parameter to the `handoff_tool` function."]}, {"cell_type": "code", "execution_count": 18, "id": "dc1e4d24-8738-4548-a5e7-8556e476b805", "metadata": {}, "outputs": [], "source": ["from langgraph.types import Send\n", "\n", "\n", "def create_task_description_handoff_tool(\n", "    *, agent_name: str, description: str | None = None\n", "):\n", "    name = f\"transfer_to_{agent_name}\"\n", "    description = description or f\"Ask {agent_name} for help.\"\n", "\n", "    @tool(name, description=description)\n", "    def handoff_tool(\n", "        # this is populated by the supervisor <PERSON><PERSON>\n", "        task_description: Annotated[\n", "            str,\n", "            \"Description of what the next agent should do, including all of the relevant context.\",\n", "        ],\n", "        # these parameters are ignored by the LLM\n", "        state: Annotated[MessagesState, InjectedState],\n", "    ) -> Command:\n", "        task_description_message = {\"role\": \"user\", \"content\": task_description}\n", "        agent_input = {**state, \"messages\": [task_description_message]}\n", "        return Command(\n", "            # highlight-next-line\n", "            goto=[Send(agent_name, agent_input)],\n", "            graph=Command.PARENT,\n", "        )\n", "\n", "    return handoff_tool\n", "\n", "\n", "assign_to_research_agent_with_description = create_task_description_handoff_tool(\n", "    agent_name=\"research_agent\",\n", "    description=\"Assign task to a researcher agent.\",\n", ")\n", "\n", "assign_to_math_agent_with_description = create_task_description_handoff_tool(\n", "    agent_name=\"math_agent\",\n", "    description=\"Assign task to a math agent.\",\n", ")\n", "\n", "supervisor_agent_with_description = create_react_agent(\n", "    model=\"openai:gpt-4.1\",\n", "    tools=[\n", "        assign_to_research_agent_with_description,\n", "        assign_to_math_agent_with_description,\n", "    ],\n", "    prompt=(\n", "        \"You are a supervisor managing two agents:\\n\"\n", "        \"- a research agent. Assign research-related tasks to this assistant\\n\"\n", "        \"- a math agent. Assign math-related tasks to this assistant\\n\"\n", "        \"Assign work to one agent at a time, do not call agents in parallel.\\n\"\n", "        \"Do not do any work yourself.\"\n", "    ),\n", "    name=\"supervisor\",\n", ")\n", "\n", "supervisor_with_description = (\n", "    StateGraph(MessagesState)\n", "    .add_node(\n", "        supervisor_agent_with_description, destinations=(\"research_agent\", \"math_agent\")\n", "    )\n", "    .add_node(research_agent)\n", "    .add_node(math_agent)\n", "    .add_edge(START, \"supervisor\")\n", "    .add_edge(\"research_agent\", \"supervisor\")\n", "    .add_edge(\"math_agent\", \"supervisor\")\n", "    .compile()\n", ")"]}, {"cell_type": "markdown", "id": "622bc89a-0787-41ea-bf18-c591be85509b", "metadata": {}, "source": ["!!! note\n", "\n", "    We're using [`Send()`][langgraph.types.Send] primitive in the `handoff_tool`. This means that instead of receiving the full `supervisor` graph state as input, each worker agent only sees the contents of the `Send` payload. In this example, we're sending the task description as a single \"human\" message."]}, {"cell_type": "markdown", "id": "4d1dba40-c948-4117-9df6-5bdb1ab162c7", "metadata": {}, "source": ["Let's now running it with the same input query:"]}, {"cell_type": "code", "execution_count": 19, "id": "882f9e00-5fdf-4278-ab26-a59fc949fa91", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Update from subgraph supervisor:\n", "\n", "\n", "\tUpdate from node agent:\n", "\n", "\n", "\t==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\tName: supervisor\n", "\tTool Calls:\n", "\t  transfer_to_research_agent (call_tk8q8py8qK6MQz6Kj6mijKua)\n", "\t Call ID: call_tk8q8py8qK6MQz6Kj6mijKua\n", "\t  Args:\n", "\t    task_description: Find the 2024 GDP (Gross Domestic Product) for both the United States and New York state, using the most up-to-date and reputable sources available. Provide both GDP values and cite the data sources.\n", "\n", "\n", "Update from subgraph research_agent:\n", "\n", "\n", "\tUpdate from node agent:\n", "\n", "\n", "\t==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\tName: research_agent\n", "\tTool Calls:\n", "\t  tavily_search (call_KqvhSvOIhAvXNsT6BOwbPlRB)\n", "\t Call ID: call_KqvhSvOIhAvXNsT6BOwbPlRB\n", "\t  Args:\n", "\t    query: 2024 United States GDP value from a reputable source\n", "\t    search_depth: advanced\n", "\t  tavily_search (call_kbbAWBc9KwCWKHmM5v04H88t)\n", "\t Call ID: call_kbbAWBc9KwCWKHmM5v04H88t\n", "\t  Args:\n", "\t    query: 2024 New York state GDP value from a reputable source\n", "\t    search_depth: advanced\n", "\n", "\n", "Update from subgraph research_agent:\n", "\n", "\n", "\tUpdate from node tools:\n", "\n", "\n", "\t=================================\u001b[1m Tool Message \u001b[0m=================================\n", "\tName: tavily_search\n", "\t\n", "\t{\"query\": \"2024 United States GDP value from a reputable source\", \"follow_up_questions\": null, \"answer\": null, \"images\": [], \"results\": [{\"url\": \"https://www.focus-economics.com/countries/united-states/\", \"title\": \"United States Economy Overview - Focus Economics\", \"content\": \"The United States' Macroeconomic Analysis:\\n------------------------------------------\\n\\n**Nominal GDP of USD 29,185 billion in 2024.**\\n\\n**Nominal GDP of USD 29,179 billion in 2024.**\\n\\n**GDP per capita of USD 86,635 compared to the global average of USD 10,589.**\\n\\n**GDP per capita of USD 86,652 compared to the global average of USD 10,589.**\\n\\n**Average real GDP growth of 2.5% over the last decade.**\\n\\n**Average real GDP growth of 2.5% over the last decade.**\\n\\nShare of the region's population [...] |  | 2020 | 2021 | 2022 | 2023 | 2024 |\\n| --- | --- | --- | --- | --- | --- |\\n| [Population (million)](https://www.focus-economics.com/country-indicator/united-states/population/) | 331 | 332 | 334 | 337 | 340 |\\n| [GDP (USD bn)](https://www.focus-economics.com/country-indicator/united-states/gdp/) | 21,354 | 23,681 | 26,007 | 27,721 | 29,185 |\", \"score\": 0.73981786, \"raw_content\": null}, {\"url\": \"https://tradingeconomics.com/united-states/gdp\", \"title\": \"United States GDP - Trading Economics\", \"content\": \"| Related | Last | Previous | Unit | Reference |\\n| --- | --- | --- | --- | --- |\\n| [Full Year GDP Growth](/united-states/full-year-gdp-growth) | 2.80 | 2.90 | percent | Dec 2024 |\\n| [GDP](/united-states/gdp) | 27720.71 | 26006.89 | USD Billion | Dec 2023 |\\n| [GDP Annual Growth Rate](/united-states/gdp-growth-annual) | 2.10 | 2.50 | percent | Mar 2025 |\\n| [GDP Constant Prices](/united-states/gdp-constant-prices) | 23528.00 | 23542.30 | USD Billion | Mar 2025 |\", \"score\": 0.65359193, \"raw_content\": null}, {\"url\": \"https://fred.stlouisfed.org/series/GDP\", \"title\": \"Gross Domestic Product (GDP) | FRED | St. Louis Fed\", \"content\": \"Q1 2025: 29,976.638 |\\nBillions of Dollars, Seasonally Adjusted Annual Rate |\\nQuarterly\\n\\nUpdated:\\nMay 29, 2025\\n7:56 AM CDT\\n\\n[Next Release Date:\\nJun 26, 2025](/releases/calendar?rid=53&y=2025)\\n\\nObservations\\n\\n|  |  |  |\\n| --- | --- | --- |\\n| Q1 2025: | 29,976.638 |  |\\n| Q4 2024: | 29,723.864 |  |\\n| Q3 2024: | 29,374.914 |  |\\n| Q2 2024: | 29,016.714 |  |\\n| Q1 2024: | 28,624.069 |  |\\n| [View All](/data/GDP.txt) | |\\n\\nUnits:\\n\\nFrequency:\", \"score\": 0.6152965, \"raw_content\": null}], \"response_time\": 2.53}\n", "\n", "\n", "Update from subgraph research_agent:\n", "\n", "\n", "\tUpdate from node tools:\n", "\n", "\n", "\t=================================\u001b[1m Tool Message \u001b[0m=================================\n", "\tName: tavily_search\n", "\t\n", "\t{\"query\": \"2024 New York state GDP value from a reputable source\", \"follow_up_questions\": null, \"answer\": null, \"images\": [], \"results\": [{\"url\": \"https://www.ibisworld.com/united-states/economic-profiles/new-york/\", \"title\": \"New York Economic Trends, Stats & Rankings | IBISWorld\", \"content\": \"#### What is New York's Gross Domestic Product (GDP)?\\n\\nIn 2024, New York's GDP reached $1.8tn, representing an increase of 1.3% from 2023. New York's GDP has grown at an annualized rate of 2.9% over the five years to 2024. Moreover, New York's trailing five-year GDP growth ranks it 37th out of all 50 US states. [...] In 2024, the state of New York has a population of 19,482,372, having grown an annualized 0.0% over the five years to 2024, which ranks it 44th out of all 50 US states by growth rate. New York's gross state product (GSP) in 2024 reached $1.8tn, with growth of 1.3% over the 5 years to 2024. Businesses in New York employed a total of 11,671,845 people in 2024, with average annual employment growth over the past five years of 0.0%. The top three sectors by total employment are Finance and [...] The Finance and Insurance, Real Estate and Rental and Leasing and Information sectors contributed the most to New York's GDP in 2024, representing a combined 46.3% of state GDP.\\n\\nGDP trends by sector are an important indicator of which sectors are contributing the most value-add to the state's economy, in addition to how the state economy is evolving over time.\\n\\nSector\\n\\nGDP\\n\\nGrowth 2024 (%)\\n\\nAnnualized Growth 2019-24\", \"score\": 0.8051581, \"raw_content\": null}, {\"url\": \"https://comptroller.nyc.gov/reports/annual-state-of-the-citys-economy-and-finances-2024/\", \"title\": \"Annual State of the City's Economy and Finances 2024\", \"content\": \"(constant 2019 dollars)**22,550 15,322 13,596 19,451 21,211 21,096\\n**_% change_**(32.1%)(11.3%)43.1%9.0%(0.5%)\\n\\nSource: NY State Department of Taxation and Finance and Office of the New York City Comptroller [...] [[1]](https://comptroller.nyc.gov/reports/annual-state-of-the-citys-economy-and-finances-2024/#_ftnref1) At that time, the Comptroller’s Office expected five-year cumulative real GDP growth, 2020 to 2024, of 8.7 percent while the mayor expected 9.5 percent.\\n\\n[[2]](https://comptroller.nyc.gov/reports/annual-state-of-the-citys-economy-and-finances-2024/#_ftnref2) U.S. Bureau of Labor Statistics Local Area Unemployment Statistics (LAUS). [...] **($ in millions)****FY 2025****FY 2026****FY 2027****FY 2028****Total****Change from June 2024 (FY2025-FY2028)****Source as Share of Total**\\n**General Obligation Bonds**$6,900$6,230$6,920$7,110$27,160$1,960 42.8%\\n**TFA FTS Bonds**6,900 6,230 6,920 7,110 27,160 2,460 42.8%\\n**NYC Water Finance Authority**1,726 2,474 2,452 2,505 9,157 435 14.4%\\n**Total****$15,526****$14,934****$16,292****$16,725****$63,477****$4,855****100.0%**\", \"score\": 0.74541736, \"raw_content\": null}], \"response_time\": 3.45}\n", "\n", "\n", "Update from subgraph research_agent:\n", "\n", "\n", "\tUpdate from node agent:\n", "\n", "\n", "\t==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\tName: research_agent\n", "\t\n", "\t- United States 2024 GDP: $29.2 trillion (USD)  \n", "\t  Source: [Focus Economics](https://www.focus-economics.com/countries/united-states/)\n", "\t\n", "\t- New York State 2024 GDP: $1.8 trillion (USD)  \n", "\t  Source: [IBISWorld](https://www.ibisworld.com/united-states/economic-profiles/new-york/)\n", "\n", "\n", "Update from subgraph supervisor:\n", "\n", "\n", "\tUpdate from node agent:\n", "\n", "\n", "\t==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\tName: supervisor\n", "\tTool Calls:\n", "\t  transfer_to_math_agent (call_EqT01HDOQDd5Timk89cccdXP)\n", "\t Call ID: call_EqT01HDOQDd5Timk89cccdXP\n", "\t  Args:\n", "\t    task_description: Calculate what percentage of the US GDP ($29.2 trillion) was New York State's GDP ($1.8 trillion) in 2024. Show your working and round the final answer to two decimal points.\n", "\n", "\n", "Update from subgraph math_agent:\n", "\n", "\n", "\tUpdate from node agent:\n", "\n", "\n", "\t==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\tName: math_agent\n", "\tTool Calls:\n", "\t  divide (call_OpdUlYEFqk4RZ2rF46M9eTrS)\n", "\t Call ID: call_OpdUlYEFqk4RZ2rF46M9eTrS\n", "\t  Args:\n", "\t    a: 1.8\n", "\t    b: 29.2\n", "\n", "\n", "Update from subgraph math_agent:\n", "\n", "\n", "\tUpdate from node tools:\n", "\n", "\n", "\t=================================\u001b[1m Tool Message \u001b[0m=================================\n", "\tName: divide\n", "\t\n", "\t0.06164383561643836\n", "\n", "\n", "Update from subgraph math_agent:\n", "\n", "\n", "\tUpdate from node agent:\n", "\n", "\n", "\t==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\tName: math_agent\n", "\tTool Calls:\n", "\t  multiply (call_vdKJTLtIzmG6JbPcGQn93MR6)\n", "\t Call ID: call_vdKJTLtIzmG6JbPcGQn93MR6\n", "\t  Args:\n", "\t    a: 0.06164383561643836\n", "\t    b: 100\n", "\n", "\n", "Update from subgraph math_agent:\n", "\n", "\n", "\tUpdate from node tools:\n", "\n", "\n", "\t=================================\u001b[1m Tool Message \u001b[0m=================================\n", "\tName: multiply\n", "\t\n", "\t6.164383561643836\n", "\n", "\n", "Update from subgraph math_agent:\n", "\n", "\n", "\tUpdate from node agent:\n", "\n", "\n", "\t==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\tName: math_agent\n", "\t\n", "\tNew York State's GDP was approximately 6.16% of the US GDP in 2024.\n", "\n", "\n", "Update from subgraph supervisor:\n", "\n", "\n", "\tUpdate from node agent:\n", "\n", "\n", "\t==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\tName: supervisor\n", "\t\n", "\tNew York State’s GDP was approximately 6.16% of the US GDP in 2024.\n", "\t\n", "\t**Working:**\n", "\t- Percentage = (NY GDP / US GDP) × 100\n", "\t- = ($1.8 trillion / $29.2 trillion) × 100\n", "\t- = 0.06164 × 100\n", "\t- = 6.16% (rounded to two decimal points)\n", "\n", "\n"]}], "source": ["for chunk in supervisor_with_description.stream(\n", "    {\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"find US and New York state GDP in 2024. what % of US GDP was New York state?\",\n", "            }\n", "        ]\n", "    },\n", "    subgraphs=True,\n", "):\n", "    pretty_print_messages(chunk, last_message=True)"]}, {"cell_type": "code", "execution_count": null, "id": "6d3916d1-7de9-47c1-ae6f-80865b804e77", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}