{"cells": [{"attachments": {"683fae34-980f-43f0-a9c2-9894bebd9157.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "8889a307-fa3f-4d38-9127-d41e4686ae47", "metadata": {}, "source": ["# Corrective RAG (CRAG)\n", "\n", "Corrective-RAG (CRAG) is a strategy for RAG that incorporates self-reflection / self-grading on retrieved documents. \n", "\n", "In the paper [here](https://arxiv.org/pdf/2401.15884.pdf), a few steps are taken:\n", "\n", "* If at least one document exceeds the threshold for relevance, then it proceeds to generation\n", "* Before generation, it performs knowledge refinement\n", "* This partitions the document into \"knowledge strips\"\n", "* It grades each strip, and filters our irrelevant ones\n", "* If all documents fall below the relevance threshold or if the grader is unsure, then the framework seeks an additional datasource\n", "* It will use web search to supplement retrieval\n", " \n", "We will implement some of these ideas from scratch using [LangGraph](https://langchain-ai.github.io/langgraph/):\n", "\n", "* Let's skip the knowledge refinement phase as a first pass. This can be added back as a node, if desired. \n", "* If *any* documents are irrelevant, let's opt to supplement retrieval with web search. \n", "* We'll use [Tavily Search](https://python.langchain.com/docs/integrations/tools/tavily_search/) for web search.\n", "* Let's use query re-writing to optimize the query for web search.\n", "\n", "![Screenshot 2024-04-01 at 9.28.30 AM.png](attachment:683fae34-980f-43f0-a9c2-9894bebd9157.png)"]}, {"cell_type": "markdown", "id": "4931ac25-99f9-4f04-b3d1-4683f7853667", "metadata": {}, "source": ["## Setup\n", "\n", "First, let's download our required packages and set our API keys"]}, {"cell_type": "code", "execution_count": null, "id": "568c84d6-9df6-4b7b-b50d-476c0a64a04b", "metadata": {}, "outputs": [], "source": ["! pip install langchain_community tiktoken langchain-openai langchainhub chromadb langchain langgraph tavily-python"]}, {"cell_type": "code", "execution_count": null, "id": "74710419-158d-4270-931c-de83db7b580d", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(key: str):\n", "    if key not in os.environ:\n", "        os.environ[key] = getpass.getpass(f\"{key}:\")\n", "\n", "\n", "_set_env(\"OPENAI_API_KEY\")\n", "_set_env(\"TAVILY_API_KEY\")"]}, {"cell_type": "markdown", "id": "3adde047", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>"]}, {"cell_type": "markdown", "id": "a21f32d2-92ce-4995-b309-99347bafe3be", "metadata": {}, "source": ["## Create Index\n", " \n", "Let's index 3 blog posts."]}, {"cell_type": "code", "execution_count": 1, "id": "3a566a30-cf0e-4330-ad4d-9bf994bdfa86", "metadata": {}, "outputs": [], "source": ["from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_community.vectorstores import Chroma\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "urls = [\n", "    \"https://lilianweng.github.io/posts/2023-06-23-agent/\",\n", "    \"https://lilianweng.github.io/posts/2023-03-15-prompt-engineering/\",\n", "    \"https://lilianweng.github.io/posts/2023-10-25-adv-attack-llm/\",\n", "]\n", "\n", "docs = [WebBaseLoader(url).load() for url in urls]\n", "docs_list = [item for sublist in docs for item in sublist]\n", "\n", "text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(\n", "    chunk_size=250, chunk_overlap=0\n", ")\n", "doc_splits = text_splitter.split_documents(docs_list)\n", "\n", "# Add to vectorDB\n", "vectorstore = Chroma.from_documents(\n", "    documents=doc_splits,\n", "    collection_name=\"rag-chroma\",\n", "    embedding=OpenAIEmbeddings(),\n", ")\n", "retriever = vectorstore.as_retriever()"]}, {"cell_type": "markdown", "id": "6fca2db8-8d68-42b0-981d-4be5ccdbe293", "metadata": {}, "source": ["## LLMs"]}, {"cell_type": "markdown", "id": "c1da8975-f2c4-4584-a0f9-bd5af88983a3", "metadata": {}, "source": ["<div class=\"admonition note\">\n", "    <p class=\"admonition-title\">Using Pydantic with <PERSON><PERSON><PERSON><PERSON></p>\n", "    <p>\n", "        This notebook uses Pydantic v2 <code>BaseModel</code>, which requires <code>langchain-core >= 0.3</code>. Using <code>langchain-core < 0.3</code> will result in errors due to mixing of Pydantic v1 and v2 <code>BaseModels</code>.\n", "    </p>\n", "</div>"]}, {"cell_type": "code", "execution_count": 5, "id": "7ece414c-2df5-4ffd-aa82-550a65775261", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["binary_score='yes'\n"]}], "source": ["### Retrieval Grader\n", "\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "# Data model\n", "class GradeDocuments(BaseModel):\n", "    \"\"\"Binary score for relevance check on retrieved documents.\"\"\"\n", "\n", "    binary_score: str = Field(\n", "        description=\"Documents are relevant to the question, 'yes' or 'no'\"\n", "    )\n", "\n", "\n", "# LLM with function call\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\", temperature=0)\n", "structured_llm_grader = llm.with_structured_output(GradeDocuments)\n", "\n", "# Prompt\n", "system = \"\"\"You are a grader assessing relevance of a retrieved document to a user question. \\n \n", "    If the document contains keyword(s) or semantic meaning related to the question, grade it as relevant. \\n\n", "    Give a binary score 'yes' or 'no' score to indicate whether the document is relevant to the question.\"\"\"\n", "grade_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system),\n", "        (\"human\", \"Retrieved document: \\n\\n {document} \\n\\n User question: {question}\"),\n", "    ]\n", ")\n", "\n", "retrieval_grader = grade_prompt | structured_llm_grader\n", "question = \"agent memory\"\n", "docs = retriever.invoke(question)\n", "doc_txt = docs[1].page_content\n", "print(retrieval_grader.invoke({\"question\": question, \"document\": doc_txt}))"]}, {"cell_type": "code", "execution_count": 6, "id": "a207c85f-e414-46b7-8999-4c0ead1493da", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The design of generative agents combines LLM with memory, planning, and reflection mechanisms to enable agents to behave conditioned on past experience. Memory stream is a long-term memory module that records a comprehensive list of agents' experience in natural language. Short-term memory is utilized for in-context learning, while long-term memory allows agents to retain and recall information over extended periods.\n"]}], "source": ["### Generate\n", "\n", "from langchain import hub\n", "from langchain_core.output_parsers import StrOutputParser\n", "\n", "# Prompt\n", "prompt = hub.pull(\"rlm/rag-prompt\")\n", "\n", "# LLM\n", "llm = ChatOpenAI(model_name=\"gpt-3.5-turbo\", temperature=0)\n", "\n", "\n", "# Post-processing\n", "def format_docs(docs):\n", "    return \"\\n\\n\".join(doc.page_content for doc in docs)\n", "\n", "\n", "# Chain\n", "rag_chain = prompt | llm | StrOutputParser()\n", "\n", "# Run\n", "generation = rag_chain.invoke({\"context\": docs, \"question\": question})\n", "print(generation)"]}, {"cell_type": "code", "execution_count": 7, "id": "30d0a69b-9087-4f85-af26-cab55b567872", "metadata": {}, "outputs": [{"data": {"text/plain": ["'What is the role of memory in artificial intelligence agents?'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["### Question Re-writer\n", "\n", "# LLM\n", "llm = ChatOpenAI(model=\"gpt-3.5-turbo-0125\", temperature=0)\n", "\n", "# Prompt\n", "system = \"\"\"You a question re-writer that converts an input question to a better version that is optimized \\n \n", "     for web search. Look at the input and try to reason about the underlying semantic intent / meaning.\"\"\"\n", "re_write_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system),\n", "        (\n", "            \"human\",\n", "            \"Here is the initial question: \\n\\n {question} \\n Formulate an improved question.\",\n", "        ),\n", "    ]\n", ")\n", "\n", "question_rewriter = re_write_prompt | llm | StrOutputParser()\n", "question_rewriter.invoke({\"question\": question})"]}, {"cell_type": "markdown", "id": "e4538467-4a15-4733-b93c-2b79d4d6bf25", "metadata": {}, "source": ["## Web Search Tool"]}, {"cell_type": "code", "execution_count": 38, "id": "46d51b53-54a9-4e0a-9f14-e39998f5b340", "metadata": {}, "outputs": [], "source": ["### Search\n", "\n", "from langchain_community.tools.tavily_search import TavilySearchResults\n", "\n", "web_search_tool = TavilySearchResults(k=3)"]}, {"cell_type": "markdown", "id": "87194a1b-535a-4593-ab95-5736fae176d1", "metadata": {}, "source": ["## Create Graph \n", "\n", "Now let's create our graph that will use CRAG\n", "\n", "### Define Graph State"]}, {"cell_type": "code", "execution_count": 39, "id": "94b3945f-ef0f-458d-a443-f763903550b0", "metadata": {}, "outputs": [], "source": ["from typing import List\n", "\n", "from typing_extensions import TypedDict\n", "\n", "\n", "class GraphState(TypedDict):\n", "    \"\"\"\n", "    Represents the state of our graph.\n", "\n", "    Attributes:\n", "        question: question\n", "        generation: LLM generation\n", "        web_search: whether to add search\n", "        documents: list of documents\n", "    \"\"\"\n", "\n", "    question: str\n", "    generation: str\n", "    web_search: str\n", "    documents: List[str]"]}, {"cell_type": "code", "execution_count": 40, "id": "efd639c5-82e2-45e6-a94a-6a4039646ef5", "metadata": {}, "outputs": [], "source": ["from langchain.schema import Document\n", "\n", "\n", "def retrieve(state):\n", "    \"\"\"\n", "    Retrieve documents\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): New key added to state, documents, that contains retrieved documents\n", "    \"\"\"\n", "    print(\"---RETRIEVE---\")\n", "    question = state[\"question\"]\n", "\n", "    # Retrieval\n", "    documents = retriever.invoke(question)\n", "    return {\"documents\": documents, \"question\": question}\n", "\n", "\n", "def generate(state):\n", "    \"\"\"\n", "    Generate answer\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): New key added to state, generation, that contains LLM generation\n", "    \"\"\"\n", "    print(\"---GENERATE---\")\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "\n", "    # RAG generation\n", "    generation = rag_chain.invoke({\"context\": documents, \"question\": question})\n", "    return {\"documents\": documents, \"question\": question, \"generation\": generation}\n", "\n", "\n", "def grade_documents(state):\n", "    \"\"\"\n", "    Determines whether the retrieved documents are relevant to the question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): Updates documents key with only filtered relevant documents\n", "    \"\"\"\n", "\n", "    print(\"---<PERSON><PERSON><PERSON> DOCUMENT RELEVANCE TO QUESTION---\")\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "\n", "    # Score each doc\n", "    filtered_docs = []\n", "    web_search = \"No\"\n", "    for d in documents:\n", "        score = retrieval_grader.invoke(\n", "            {\"question\": question, \"document\": d.page_content}\n", "        )\n", "        grade = score.binary_score\n", "        if grade == \"yes\":\n", "            print(\"---GRADE: DOCUMENT RELEVANT---\")\n", "            filtered_docs.append(d)\n", "        else:\n", "            print(\"---GRADE: DOCUMENT NOT RELEVANT---\")\n", "            web_search = \"Yes\"\n", "            continue\n", "    return {\"documents\": filtered_docs, \"question\": question, \"web_search\": web_search}\n", "\n", "\n", "def transform_query(state):\n", "    \"\"\"\n", "    Transform the query to produce a better question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): Updates question key with a re-phrased question\n", "    \"\"\"\n", "\n", "    print(\"---TRANSFORM QUERY---\")\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "\n", "    # Re-write question\n", "    better_question = question_rewriter.invoke({\"question\": question})\n", "    return {\"documents\": documents, \"question\": better_question}\n", "\n", "\n", "def web_search(state):\n", "    \"\"\"\n", "    Web search based on the re-phrased question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): Updates documents key with appended web results\n", "    \"\"\"\n", "\n", "    print(\"---WEB SEARCH---\")\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "\n", "    # Web search\n", "    docs = web_search_tool.invoke({\"query\": question})\n", "    web_results = \"\\n\".join([d[\"content\"] for d in docs])\n", "    web_results = Document(page_content=web_results)\n", "    documents.append(web_results)\n", "\n", "    return {\"documents\": documents, \"question\": question}\n", "\n", "\n", "### Edges\n", "\n", "\n", "def decide_to_generate(state):\n", "    \"\"\"\n", "    Determines whether to generate an answer, or re-generate a question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        str: Binary decision for next node to call\n", "    \"\"\"\n", "\n", "    print(\"---ASSESS GRADED DOCUMENTS---\")\n", "    state[\"question\"]\n", "    web_search = state[\"web_search\"]\n", "    state[\"documents\"]\n", "\n", "    if web_search == \"Yes\":\n", "        # All documents have been filtered check_relevance\n", "        # We will re-generate a new query\n", "        print(\n", "            \"---DECISION: AL<PERSON> DOCUMENTS ARE NOT RELEVANT TO QUESTION, TRANSFORM QUERY---\"\n", "        )\n", "        return \"transform_query\"\n", "    else:\n", "        # We have relevant documents, so generate answer\n", "        print(\"---DECISION: GENERATE---\")\n", "        return \"generate\""]}, {"cell_type": "markdown", "id": "fa076e90-7132-4fcf-8507-db5990314c4f", "metadata": {}, "source": ["### Compile Graph\n", "\n", "The just follows the flow we outlined in the figure above."]}, {"cell_type": "code", "execution_count": 41, "id": "dedae17a-98c6-474d-90a7-9234b7c8cea0", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import END, StateGraph, START\n", "\n", "workflow = StateGraph(GraphState)\n", "\n", "# Define the nodes\n", "workflow.add_node(\"retrieve\", retrieve)  # retrieve\n", "workflow.add_node(\"grade_documents\", grade_documents)  # grade documents\n", "workflow.add_node(\"generate\", generate)  # generate\n", "workflow.add_node(\"transform_query\", transform_query)  # transform_query\n", "workflow.add_node(\"web_search_node\", web_search)  # web search\n", "\n", "# Build graph\n", "workflow.add_edge(START, \"retrieve\")\n", "workflow.add_edge(\"retrieve\", \"grade_documents\")\n", "workflow.add_conditional_edges(\n", "    \"grade_documents\",\n", "    decide_to_generate,\n", "    {\n", "        \"transform_query\": \"transform_query\",\n", "        \"generate\": \"generate\",\n", "    },\n", ")\n", "workflow.add_edge(\"transform_query\", \"web_search_node\")\n", "workflow.add_edge(\"web_search_node\", \"generate\")\n", "workflow.add_edge(\"generate\", END)\n", "\n", "# Compile\n", "app = workflow.compile()"]}, {"cell_type": "markdown", "id": "27ba16a8", "metadata": {}, "source": ["## Use the graph"]}, {"cell_type": "code", "execution_count": 42, "id": "f5b7c2fe-1fc7-4b76-bf93-ba701a40aa6b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---RETRIEVE---\n", "\"Node 'retrieve':\"\n", "'\\n---\\n'\n", "---<PERSON><PERSON><PERSON> DOCUMENT R<PERSON>EVANCE TO QUESTION---\n", "---GRADE: DOCUMENT NOT RELEVANT---\n", "---GRADE: DOCUMENT NOT RELEVANT---\n", "---GRADE: DOCUMENT RELEVANT---\n", "---GRADE: DOCUMENT RELEVANT---\n", "\"Node 'grade_documents':\"\n", "'\\n---\\n'\n", "---ASSESS GRADED DOCUMENTS---\n", "---DECISION: <PERSON><PERSON> DOCUMENTS ARE NOT RELEVANT TO QUESTION, TRANSFORM QUERY---\n", "---TRANSFORM QUERY---\n", "\"Node 'transform_query':\"\n", "'\\n---\\n'\n", "---WEB SEARCH---\n", "\"Node 'web_search_node':\"\n", "'\\n---\\n'\n", "---GENERATE---\n", "\"Node 'generate':\"\n", "'\\n---\\n'\n", "\"Node '__end__':\"\n", "'\\n---\\n'\n", "('Agents possess short-term memory, which is utilized for in-context learning, '\n", " 'and long-term memory, allowing them to retain and recall vast amounts of '\n", " 'information over extended periods. Some experts also classify working memory '\n", " 'as a distinct type, although it can be considered a part of short-term '\n", " 'memory in many cases.')\n"]}], "source": ["from pprint import pprint\n", "\n", "# Run\n", "inputs = {\"question\": \"What are the types of agent memory?\"}\n", "for output in app.stream(inputs):\n", "    for key, value in output.items():\n", "        # Node\n", "        pprint(f\"Node '{key}':\")\n", "        # Optional: print full state at each node\n", "        # pprint.pprint(value[\"keys\"], indent=2, width=80, depth=None)\n", "    pprint(\"\\n---\\n\")\n", "\n", "# Final generation\n", "pprint(value[\"generation\"])"]}, {"cell_type": "code", "execution_count": 43, "id": "41ea1108-f385-4774-962d-db157922e231", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---RETRIEVE---\n", "\"Node 'retrieve':\"\n", "'\\n---\\n'\n", "---<PERSON><PERSON><PERSON> DOCUMENT R<PERSON>EVANCE TO QUESTION---\n", "---GRADE: DOCUMENT NOT RELEVANT---\n", "---GRADE: DOCUMENT NOT RELEVANT---\n", "---GRADE: DOCUMENT NOT RELEVANT---\n", "---GRADE: DOCUMENT RELEVANT---\n", "\"Node 'grade_documents':\"\n", "'\\n---\\n'\n", "---ASSESS GRADED DOCUMENTS---\n", "---DECISION: <PERSON><PERSON> DOCUMENTS ARE NOT RELEVANT TO QUESTION, TRANSFORM QUERY---\n", "---TRANSFORM QUERY---\n", "\"Node 'transform_query':\"\n", "'\\n---\\n'\n", "---WEB SEARCH---\n", "\"Node 'web_search_node':\"\n", "'\\n---\\n'\n", "---GENERATE---\n", "\"Node 'generate':\"\n", "'\\n---\\n'\n", "\"Node '__end__':\"\n", "'\\n---\\n'\n", "('The AlphaCodium paper functions by proposing a code-oriented iterative flow '\n", " 'that involves repeatedly running and fixing generated code against '\n", " 'input-output tests. Its key mechanisms include generating additional data '\n", " 'like problem reflection and test reasoning to aid the iterative process, as '\n", " 'well as enriching the code generation process. AlphaCodium aims to improve '\n", " 'the performance of Large Language Models on code problems by following a '\n", " 'test-based, multi-stage approach.')\n"]}], "source": ["from pprint import pprint\n", "\n", "# Run\n", "inputs = {\"question\": \"How does the AlphaCodium paper work?\"}\n", "for output in app.stream(inputs):\n", "    for key, value in output.items():\n", "        # Node\n", "        pprint(f\"Node '{key}':\")\n", "        # Optional: print full state at each node\n", "        # pprint.pprint(value[\"keys\"], indent=2, width=80, depth=None)\n", "    pprint(\"\\n---\\n\")\n", "\n", "# Final generation\n", "pprint(value[\"generation\"])"]}, {"cell_type": "markdown", "id": "a7e44593-1959-4abf-8405-5e23aa9398f5", "metadata": {}, "source": ["LangSmith Traces - \n", " \n", "* https://smith.langchain.com/public/f6b1716c-e842-4282-9112-1026b93e246b/r\n", "\n", "* https://smith.langchain.com/public/497c8ed9-d9e2-429e-8ada-e64de3ec26c9/r"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}