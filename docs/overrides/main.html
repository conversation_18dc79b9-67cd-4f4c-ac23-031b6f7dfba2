{% extends "base.html" %}

{% block analytics %}
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-T35S4S46');</script>
<!-- End Google Tag Manager -->
{% endblock %}


{% block extrahead %}
  <meta name="algolia-site-verification" content="165B7E7C89E49946" />
  <style>
    @import url("https://fonts.googleapis.com/css2?family=Public+Sans&display=swap");
    :root {
      --md-primary-fg-color: #333333;
      --md-accent-fg-color: #1E88E5;
      --md-default-bg-color: #FFFFFF;
      --md-default-fg-color: #333333;
      --md-text-font-family: "Public Sans", sans-serif;
    }

    body {
      font-family: var(--md-text-font-family);
      background-color: var(--md-default-bg-color);
      color: var(--md-default-fg-color);
    }

    .md-main {
      background-color: #FFFFFF;
    }

    .md-footer {
      background-color: #F5F5F5;
      color: #666666;
    }

    .md-footer-meta {
      background-color: #4d4d4d;
    }

    .md-typeset a {
      color: #1E88E5;
    }

    .md-typeset a:hover {
      color: #1565C0;
    }

    .md-nav__link--active,
    .md-nav__link:active {
      color: #1E88E5;
    }

    .md-search__input {
      background-color: #F5F5F5;
      color: #333333;
    }

    .md-search__input:hover,
    .md-search__input:focus {
      background-color: #EEEEEE;
    }
    /* Table of contents styles */
    .md-nav--secondary .md-nav__item--active > .md-nav__link {
      font-weight: bold;
      color: var(--md-primary-fg-color);
    }

    .md-nav--secondary .md-nav__item--nested > .md-nav__link {
      font-weight: normal;
      color: var(--md-default-fg-color);
    }

    .md-nav--secondary .md-nav__item--nested > .md-nav__link::before {
      content: "";
      display: inline-block;
      width: 6px;
      height: 6px;
      background-color: var(--md-default-fg-color);
      border-radius: 50%;
      margin-right: 0.5rem;
    }

    [data-md-color-scheme="slate"] {
      --md-default-bg-color: #1E1E1E;
      --md-default-fg-color: #FFFFFF;
      --md-accent-fg-color: #64B5F6;
    }

    [data-md-color-scheme="slate"] .md-main {
      background-color: #1E1E1E;
    }

    [data-md-color-scheme="slate"] .navbar {
      background-color: #1E1E1E;
      color: #FFFFFF;
      box-shadow: none;
    }

    [data-md-color-scheme="slate"] .md-footer {
      background-color: #1E1E1E;
      color: #BDBDBD;
    }

    [data-md-color-scheme="slate"] .md-header {
      background-color: #1E1E1E;
      color: #BDBDBD;
    }

    [data-md-color-scheme="slate"] .md-tabs {
      background-color: #1E1E1E;
      color: #BDBDBD;
    }

    [data-md-color-scheme="slate"] .md-search__input {
      background-color: #F5F5F5;
      color: #333333;
    }

    [data-md-color-scheme="slate"] .md-search__icon {
      color: #333333;
    }

    [data-md-color-scheme="slate"] .md-search__input::placeholder {
      color: #333333;
    }

    [data-md-color-scheme="slate"] .md-footer-meta {
      background-color: #4d4d4d;
    }

    [data-md-color-scheme="slate"] .md-typeset a {
      color: #64B5F6;
    }

    [data-md-color-scheme="slate"] .md-typeset a:hover {
      color: #90CAF9;
    }

    .notebook-links {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 1rem;
    }
    .notebook-links .md-content__button {
      margin-left: 0.5rem;
    }

    [data-md-color-scheme=default] .logo-dark {
      display: none !important;
    }

    [data-md-color-scheme=slate] .logo-light {
      display: none !important;
    }

    .jupyter-wrapper .jp-CodeCell .jp-Cell-inputWrapper .jp-InputPrompt.jp-InputArea-prompt {
      display: none !important;
    }

    .jupyter-wrapper .jp-Notebook .jp-Cell .jp-OutputPrompt {
      display: none !important;
    }

    .md-banner {
      background-color: #CFC9FA;
      color: #000000;
    }

    .md-banner a {
      color: #000000;
      text-decoration: underline;
    }

    .md-banner a:hover {
      color: #000000;
    }

    .md-header__title {
      visibility: hidden;
    }

    /* Dark mode banner links */
    [data-md-color-scheme="slate"] .md-banner a {
      color: #000000;
    }

    [data-md-color-scheme="slate"] .md-banner a:hover {
      color: #000000;
    }

  </style>
{% endblock %}

{% block content %}
<div class="notebook-links">
  {% if page.nb_url %}
    <a href="{{ page.nb_url }}" title="Download Notebook" class="md-content__button md-icon">
      {% include ".icons/material/download.svg" %}
    </a>
  {% endif %}
</div>

{{ super() }}
{% endblock content %}


{% block htmltitle %}
  {% if page.meta and page.meta.title %}
    <title>{{ page.meta.title }}</title>
  {% elif page.title and not page.is_homepage %}
    <title>{{ page.title | striptags }}</title>
  {% else %}
    <title>{{ config.site_name }}</title>
  {% endif %}
{% endblock %}

{% block announce %}
<strong>We are growing and hiring for multiple roles for LangChain, LangGraph and LangSmith. <a href="https://www.langchain.com/careers" target="_blank" rel="noopener noreferrer"> Join our team!</a></strong>
{% endblock %}
